import numpy as np
from typing import List, Dict, Any
import hashlib
from src.utils.config import settings
from src.utils.logging import LoggingMixin
from src.utils.exceptions import EmbeddingServiceException

class MockEmbeddingService(LoggingMixin):
    """Mock embedding service that generates consistent dummy vectors"""
    
    def __init__(self):
        self.dimension = settings.EMBEDDING_DIMENSION
        self.cache = {}
        self.logger.info(f"Initialized MockEmbeddingService with dimension {self.dimension}")
    
    async def generate_embedding(self, text: str) -> List[float]:
        """Generate a consistent mock embedding for text"""
        try:
            # Use text hash to generate consistent vectors
            text_hash = hashlib.md5(text.encode()).hexdigest()
            
            if text_hash in self.cache:
                self.logger.debug(f"Cache hit for text hash: {text_hash[:8]}")
                return self.cache[text_hash]
            
            # Generate pseudo-random but consistent vector
            np.random.seed(int(text_hash[:8], 16))
            vector = np.random.normal(0, 1, self.dimension)
            
            # Normalize the vector
            vector = vector / np.linalg.norm(vector)
            
            # Cache the result
            embedding = vector.tolist()
            self.cache[text_hash] = embedding
            
            self.logger.debug(f"Generated embedding for text hash: {text_hash[:8]}")
            return embedding
            
        except Exception as e:
            self.log_error(e, {"text_length": len(text)})
            raise EmbeddingServiceException(f"Failed to generate embedding: {str(e)}")
    
    async def batch_generate_embeddings(self, texts: List[str]) -> List[List[float]]:
        """Generate embeddings for multiple texts"""
        try:
            embeddings = []
            for text in texts:
                embedding = await self.generate_embedding(text)
                embeddings.append(embedding)
            
            self.logger.info(f"Generated {len(embeddings)} embeddings in batch")
            return embeddings
            
        except Exception as e:
            self.log_error(e, {"batch_size": len(texts)})
            raise EmbeddingServiceException(f"Failed to generate batch embeddings: {str(e)}")
    
    def calculate_similarity(self, embedding1: List[float], embedding2: List[float]) -> float:
        """Calculate cosine similarity between two embeddings"""
        try:
            vec1 = np.array(embedding1)
            vec2 = np.array(embedding2)
            
            dot_product = np.dot(vec1, vec2)
            norm1 = np.linalg.norm(vec1)
            norm2 = np.linalg.norm(vec2)
            
            if norm1 == 0 or norm2 == 0:
                return 0.0
            
            similarity = dot_product / (norm1 * norm2)
            return float(similarity)
            
        except Exception as e:
            self.log_error(e)
            raise EmbeddingServiceException(f"Failed to calculate similarity: {str(e)}")
    
    async def find_similar_chunks(self, query_embedding: List[float], 
                                 chunk_embeddings: Dict[str, List[float]], 
                                 top_k: int = 10) -> List[Dict[str, Any]]:
        """Find most similar chunks to query (mock implementation)"""
        try:
            similarities = []
            for chunk_id, chunk_embedding in chunk_embeddings.items():
                similarity = self.calculate_similarity(query_embedding, chunk_embedding)
                similarities.append({
                    "chunk_id": chunk_id,
                    "similarity": similarity
                })
            
            # Sort by similarity and return top_k
            similarities.sort(key=lambda x: x["similarity"], reverse=True)
            result = similarities[:top_k]
            
            self.logger.info(f"Found {len(result)} similar chunks from {len(chunk_embeddings)} total")
            return result
            
        except Exception as e:
            self.log_error(e, {"query_dim": len(query_embedding), "total_chunks": len(chunk_embeddings)})
            raise EmbeddingServiceException(f"Failed to find similar chunks: {str(e)}")
    
    async def get_embedding_stats(self) -> Dict[str, Any]:
        """Get statistics about the embedding service"""
        return {
            "dimension": self.dimension,
            "cache_size": len(self.cache),
            "service_type": "mock",
            "status": "active"
        }
    
    def clear_cache(self) -> None:
        """Clear the embedding cache"""
        cache_size = len(self.cache)
        self.cache.clear()
        self.logger.info(f"Cleared embedding cache ({cache_size} entries)")
    
    async def health_check(self) -> bool:
        """Check if the embedding service is healthy"""
        try:
            # Test embedding generation
            test_embedding = await self.generate_embedding("health check test")
            return len(test_embedding) == self.dimension
        except Exception as e:
            self.log_error(e)
            return False

# Global embedding service instance
embedding_service = MockEmbeddingService()