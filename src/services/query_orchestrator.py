import asyncio
from typing import List, Dict, Any, Optional
import time
from datetime import datetime
import openai

from src.services.embedding_service import embedding_service
from src.services.graph_service import graph_service
from src.services.metadata_service import metadata_service
from src.services.knowledge_extraction import knowledge_extraction_service
from src.models.query import QueryRequest, QueryResponse, SourceCitation
from src.utils.config import settings
from src.utils.logging import LoggingMixin
from src.utils.exceptions import QueryProcessingException, OpenAIException

class GraphRAGOrchestrator(LoggingMixin):
    def __init__(self):
        self.embedding_service = embedding_service
        self.graph_service = graph_service
        self.metadata_service = metadata_service
        self.knowledge_service = knowledge_extraction_service
        openai.api_key = settings.OPENAI_API_KEY
        
    async def process_query(self, query_request: QueryRequest) -> QueryResponse:
        """Main GraphRAG pipeline orchestration"""
        start_time = time.time()
        
        try:
            self.logger.info(f"Processing query: {query_request.query[:100]}...")
            
            # Step 1: Initial semantic retrieval
            relevant_chunks = await self._initial_retrieval(
                query_request.query, 
                query_request.context_limit
            )
            
            # Step 2: Entity identification from chunks
            entities = await self._identify_entities(relevant_chunks)
            
            # Step 3: Graph exploration
            graph_context = await self._explore_graph(
                entities, 
                query_request.max_graph_depth
            )
            
            # Step 4: Context augmentation
            augmented_context = await self._augment_context(
                relevant_chunks, 
                graph_context
            )
            
            # Step 5: Answer synthesis
            answer = await self._synthesize_answer(
                query_request.query, 
                augmented_context
            )
            
            # Step 6: Prepare response with citations
            sources = self._prepare_citations(relevant_chunks, graph_context)
            
            processing_time = time.time() - start_time
            
            response = QueryResponse(
                answer=answer["text"],
                confidence=answer["confidence"],
                sources=sources,
                processing_time=processing_time,
                graph_nodes_explored=len(graph_context.get("nodes", [])),
                timestamp=datetime.utcnow()
            )
            
            self.logger.info(f"Query processed successfully in {processing_time:.2f}s")
            return response
            
        except Exception as e:
            processing_time = time.time() - start_time
            self.log_error(e, {"query": query_request.query[:100], "processing_time": processing_time})
            
            return QueryResponse(
                answer="I apologize, but I encountered an error processing your query. Please try again or rephrase your question.",
                confidence=0.0,
                sources=[],
                processing_time=processing_time,
                graph_nodes_explored=0,
                timestamp=datetime.utcnow()
            )
    
    async def _initial_retrieval(self, query: str, limit: int) -> List[Dict[str, Any]]:
        """Step 1: Perform initial semantic search using mock embeddings"""
        
        try:
            self.logger.debug("Starting initial retrieval")
            
            # Generate query embedding
            query_embedding = await self.embedding_service.generate_embedding(query)
            
            # Get all chunk embeddings from metadata service
            all_chunks = await self.metadata_service.get_all_chunks_with_embeddings()
            
            if not all_chunks:
                self.logger.warning("No chunks available for retrieval")
                return []
            
            # Find similar chunks using mock similarity
            chunk_embeddings = {
                chunk["id"]: chunk["embedding"] 
                for chunk in all_chunks
            }
            
            similar_chunks = await self.embedding_service.find_similar_chunks(
                query_embedding, 
                chunk_embeddings, 
                top_k=limit
            )
            
            # Enrich with chunk content
            enriched_chunks = []
            for similar_chunk in similar_chunks:
                chunk_data = await self.metadata_service.get_chunk_by_id(
                    similar_chunk["chunk_id"]
                )
                if chunk_data:
                    chunk_data["similarity"] = similar_chunk["similarity"]
                    enriched_chunks.append(chunk_data)
            
            self.logger.info(f"Retrieved {len(enriched_chunks)} relevant chunks")
            return enriched_chunks
            
        except Exception as e:
            self.log_error(e, {"query_length": len(query)})
            raise QueryProcessingException(f"Initial retrieval failed: {str(e)}")
    
    async def _identify_entities(self, chunks: List[Dict[str, Any]]) -> List[str]:
        """Step 2: Identify key entities from retrieved chunks"""
        
        try:
            if not chunks:
                return []
            
            self.logger.debug("Identifying entities from chunks")
            
            # Combine chunk texts
            combined_text = "\n\n".join([chunk["chunk_text"] for chunk in chunks[:5]])  # Limit to top 5 chunks
            
            # Use knowledge extraction service to identify entities
            entities = await self.knowledge_service.extract_entities_only(combined_text)
            
            # Extract entity names
            entity_names = [entity.name for entity in entities]
            
            self.logger.info(f"Identified {len(entity_names)} entities: {entity_names[:5]}")
            return entity_names
            
        except Exception as e:
            self.log_error(e, {"chunks_count": len(chunks)})
            # Fallback: extract entities from chunk metadata if available
            entities = []
            for chunk in chunks:
                if "entities" in chunk.get("metadata", {}):
                    entities.extend(chunk["metadata"]["entities"])
            
            return list(set(entities))[:10]
    
    async def _explore_graph(self, entities: List[str], max_depth: int) -> Dict[str, Any]:
        """Step 3: Explore graph to find related entities and relationships"""
        
        try:
            if not entities:
                self.logger.warning("No entities provided for graph exploration")
                return {"nodes": [], "relationships": []}
            
            self.logger.debug(f"Exploring graph with {len(entities)} entities, max depth {max_depth}")
            
            # Use graph service to explore subgraph
            subgraph = await self.graph_service.explore_subgraph(
                start_entities=entities,
                max_depth=max_depth
            )
            
            self.logger.info(f"Graph exploration found {subgraph['node_count']} nodes, {subgraph['relationship_count']} relationships")
            return subgraph
            
        except Exception as e:
            self.log_error(e, {"entities": entities, "max_depth": max_depth})
            return {"nodes": [], "relationships": []}
    
    async def _augment_context(self, initial_chunks: List[Dict[str, Any]], 
                              graph_context: Dict[str, Any]) -> Dict[str, Any]:
        """Step 4: Augment context with graph-related information"""
        
        try:
            self.logger.debug("Augmenting context with graph information")
            
            # Get entity names from graph context
            graph_entities = [node["name"] for node in graph_context.get("nodes", [])]
            
            # Find additional chunks that mention these entities
            additional_chunk_ids = []
            if graph_entities:
                additional_chunk_ids = await self.graph_service.get_entity_context_chunks(graph_entities)
            
            # Retrieve additional chunks
            additional_chunks = []
            for chunk_id in additional_chunk_ids[:20]:  # Limit additional chunks
                chunk_data = await self.metadata_service.get_chunk_by_id(chunk_id)
                if chunk_data and chunk_data not in initial_chunks:
                    additional_chunks.append(chunk_data)
            
            # Combine and rank all context
            all_chunks = initial_chunks + additional_chunks
            
            # Create comprehensive context
            context = {
                "primary_chunks": initial_chunks,
                "graph_chunks": additional_chunks,
                "graph_structure": graph_context,
                "total_chunks": len(all_chunks),
                "entities_explored": graph_entities
            }
            
            self.logger.info(f"Augmented context: {len(initial_chunks)} primary + {len(additional_chunks)} graph chunks")
            return context
            
        except Exception as e:
            self.log_error(e)
            # Return basic context on error
            return {
                "primary_chunks": initial_chunks,
                "graph_chunks": [],
                "graph_structure": graph_context,
                "total_chunks": len(initial_chunks),
                "entities_explored": []
            }
    
    async def _synthesize_answer(self, query: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Step 5: Generate final answer using OpenAI GPT-4"""
        
        try:
            self.logger.debug("Synthesizing answer with LLM")
            
            # Prepare context for LLM
            context_text = self._format_context_for_llm(context)
            
            system_prompt = """You are an expert knowledge assistant that provides accurate, comprehensive answers based on the provided context from a knowledge graph and document chunks.

Guidelines:
1. Use only information from the provided context
2. Synthesize information from multiple sources when relevant
3. Provide specific, factual answers
4. If the context doesn't contain enough information, say so clearly
5. Include relevant details and relationships from the knowledge graph
6. Maintain accuracy and avoid speculation

Format your response as JSON:
{
  "text": "Your comprehensive answer here",
  "confidence": 0.85,
  "key_entities": ["entity1", "entity2"],
  "reasoning": "Brief explanation of how you arrived at this answer"
}"""

            user_prompt = f"""
Query: {query}

Context Information:
{context_text}

Please provide a comprehensive answer based on this context.
"""

            response = await openai.ChatCompletion.acreate(
                model=settings.OPENAI_MODEL,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                max_tokens=settings.OPENAI_MAX_TOKENS,
                temperature=settings.OPENAI_TEMPERATURE
            )
            
            answer_text = response.choices[0].message.content
            
            # Parse JSON response
            import json
            import re
            json_match = re.search(r'\{.*\}', answer_text, re.DOTALL)
            if json_match:
                answer_data = json.loads(json_match.group(0))
                self.logger.info(f"Answer synthesized with confidence {answer_data.get('confidence', 0)}")
                return answer_data
            else:
                # Fallback if JSON parsing fails
                return {
                    "text": answer_text,
                    "confidence": 0.7,
                    "key_entities": [],
                    "reasoning": "Direct response from LLM"
                }
                
        except openai.error.OpenAIError as e:
            self.log_error(e)
            raise OpenAIException(f"OpenAI API error in answer synthesis: {str(e)}")
        except Exception as e:
            self.log_error(e)
            return {
                "text": "I apologize, but I couldn't generate a proper answer based on the available context.",
                "confidence": 0.0,
                "key_entities": [],
                "reasoning": f"Error in synthesis: {str(e)}"
            }
    
    def _format_context_for_llm(self, context: Dict[str, Any]) -> str:
        """Format the augmented context for LLM consumption"""
        
        formatted_context = []
        
        # Add primary chunks
        if context["primary_chunks"]:
            formatted_context.append("=== PRIMARY RELEVANT CONTENT ===")
            for i, chunk in enumerate(context["primary_chunks"][:5]):
                formatted_context.append(f"Source {i+1}: {chunk['chunk_text']}")
        
        # Add graph structure information
        if context["graph_structure"]["nodes"]:
            formatted_context.append("\n=== KNOWLEDGE GRAPH RELATIONSHIPS ===")
            
            # Add key entities
            entities = [node["name"] for node in context["graph_structure"]["nodes"][:10]]
            formatted_context.append(f"Key Entities: {', '.join(entities)}")
            
            # Add relationships
            relationships = context["graph_structure"]["relationships"][:15]
            if relationships:
                formatted_context.append("Key Relationships:")
                for rel in relationships:
                    formatted_context.append(f"- {rel['source']} {rel['type']} {rel['target']}")
        
        # Add additional context chunks
        if context["graph_chunks"]:
            formatted_context.append("\n=== ADDITIONAL RELATED CONTENT ===")
            for i, chunk in enumerate(context["graph_chunks"][:3]):
                formatted_context.append(f"Related {i+1}: {chunk['chunk_text'][:500]}...")
        
        return "\n".join(formatted_context)
    
    def _prepare_citations(self, chunks: List[Dict[str, Any]], 
                          graph_context: Dict[str, Any]) -> List[SourceCitation]:
        """Prepare source citations for the response"""
        
        citations = []
        
        for chunk in chunks[:5]:  # Limit citations
            citation = SourceCitation(
                document_id=chunk["document_id"],
                document_name=chunk.get("document_name", "Unknown Document"),
                chunk_id=chunk["id"],
                chunk_text=chunk["chunk_text"][:200] + "..." if len(chunk["chunk_text"]) > 200 else chunk["chunk_text"],
                relevance_score=chunk.get("similarity", 0.0)
            )
            citations.append(citation)
        
        return citations
    
    async def health_check(self) -> bool:
        """Check if the query orchestrator is healthy"""
        try:
            # Test all dependent services
            embedding_health = await self.embedding_service.health_check()
            graph_health = await self.graph_service.health_check()
            metadata_health = await self.metadata_service.health_check()
            knowledge_health = await self.knowledge_service.health_check()
            
            return all([embedding_health, graph_health, metadata_health, knowledge_health])
        except Exception as e:
            self.log_error(e)
            return False

# Global query orchestrator instance
query_orchestrator = GraphRAGOrchestrator()