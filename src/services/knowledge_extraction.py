import openai
from typing import List, Dict, Any
import json
import re
from src.models.graph import Triple, Entity, EntityType
from src.utils.config import settings
from src.utils.logging import LoggingMixin
from src.utils.exceptions import KnowledgeExtractionException, OpenAIException

class KnowledgeExtractionService(LoggingMixin):
    def __init__(self):
        openai.api_key = settings.OPENAI_API_KEY
        self.model = settings.OPENAI_MODEL
        self.max_tokens = settings.OPENAI_MAX_TOKENS
        self.temperature = settings.OPENAI_TEMPERATURE
        self.logger.info(f"Initialized KnowledgeExtractionService with model {self.model}")
        
    async def extract_triples(self, text_chunk: str) -> List[Triple]:
        """Extract knowledge triples from text using OpenAI GPT-4"""
        
        try:
            prompt = self._build_extraction_prompt(text_chunk)
            
            response = await openai.ChatCompletion.acreate(
                model=self.model,
                messages=[
                    {"role": "system", "content": self._get_system_prompt()},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=self.max_tokens,
                temperature=self.temperature
            )
            
            extracted_data = response.choices[0].message.content
            triples = self._parse_extraction_response(extracted_data)
            
            self.logger.info(f"Extracted {len(triples)} triples from text chunk")
            return triples
            
        except openai.error.OpenAIError as e:
            self.log_error(e, {"text_length": len(text_chunk)})
            raise OpenAIException(f"OpenAI API error: {str(e)}")
        except Exception as e:
            self.log_error(e, {"text_length": len(text_chunk)})
            raise KnowledgeExtractionException(f"Knowledge extraction failed: {str(e)}")
    
    def _get_system_prompt(self) -> str:
        return """You are an expert knowledge extraction system. Your task is to extract structured knowledge from text in the form of subject-predicate-object triples.

Guidelines:
1. Extract only factual, significant relationships
2. Use clear, normalized entity names
3. Use descriptive, standardized predicates
4. Assign confidence scores based on certainty
5. Classify entities by type when possible
6. Focus on core facts and meaningful connections

Return results as valid JSON array of objects with this structure:
{
  "subject": {"name": "entity_name", "type": "entity_type"},
  "predicate": "relationship_description",
  "object": {"name": "entity_name", "type": "entity_type"},
  "confidence": 0.95,
  "context": "relevant_text_snippet"
}

Entity types: person, organization, location, concept, event, other"""

    def _build_extraction_prompt(self, text: str) -> str:
        return f"""Extract all significant knowledge triples from the following text:

TEXT:
{text}

Return a JSON array of triples following the specified format. Focus on extracting:
- Key entities and their relationships
- Important facts and connections
- Hierarchical relationships
- Temporal relationships
- Causal relationships

Ensure entity names are normalized and predicates are descriptive."""

    def _parse_extraction_response(self, response: str) -> List[Triple]:
        """Parse the OpenAI response into Triple objects"""
        try:
            # Clean the response to extract JSON
            json_match = re.search(r'\[.*\]', response, re.DOTALL)
            if not json_match:
                self.logger.warning("No JSON array found in response")
                return []
            
            json_str = json_match.group(0)
            data = json.loads(json_str)
            
            triples = []
            for item in data:
                try:
                    subject = Entity(
                        name=item["subject"]["name"],
                        type=self._parse_entity_type(item["subject"].get("type", "other"))
                    )
                    object_entity = Entity(
                        name=item["object"]["name"],
                        type=self._parse_entity_type(item["object"].get("type", "other"))
                    )
                    
                    triple = Triple(
                        subject=subject,
                        relationship=item["predicate"],
                        object=object_entity,
                        confidence=item.get("confidence", 0.8),
                        context=item.get("context", "")
                    )
                    triples.append(triple)
                    
                except (KeyError, ValueError) as e:
                    self.logger.warning(f"Error parsing triple: {e}", extra={"item": item})
                    continue
            
            return triples
            
        except json.JSONDecodeError as e:
            self.logger.error(f"Error parsing JSON response: {e}", extra={"response": response[:500]})
            return []
        except Exception as e:
            self.log_error(e, {"response_length": len(response)})
            return []
    
    def _parse_entity_type(self, type_str: str) -> EntityType:
        """Parse entity type string to EntityType enum"""
        try:
            return EntityType(type_str.lower())
        except ValueError:
            return EntityType.OTHER
    
    async def extract_entities_only(self, text_chunk: str) -> List[Entity]:
        """Extract only entities from text (for entity identification)"""
        try:
            prompt = f"""
            From the following text, identify the most important entities (people, organizations, locations, concepts) that would be useful for finding related information in a knowledge graph.
            
            Return only a JSON array of entities with this structure:
            {{"name": "entity_name", "type": "entity_type", "confidence": 0.95}}
            
            Entity types: person, organization, location, concept, event, other
            
            Text:
            {text_chunk[:2000]}  # Limit text length
            """
            
            response = await openai.ChatCompletion.acreate(
                model=self.model,
                messages=[
                    {"role": "system", "content": "You are an expert entity extraction system. Return only valid JSON arrays of entities."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=500,
                temperature=0.1
            )
            
            entities_text = response.choices[0].message.content
            
            # Parse JSON response
            json_match = re.search(r'\[.*\]', entities_text, re.DOTALL)
            if json_match:
                entities_data = json.loads(json_match.group(0))
                entities = []
                
                for entity_data in entities_data:
                    try:
                        entity = Entity(
                            name=entity_data["name"],
                            type=self._parse_entity_type(entity_data.get("type", "other")),
                            confidence=entity_data.get("confidence", 0.8)
                        )
                        entities.append(entity)
                    except (KeyError, ValueError) as e:
                        self.logger.warning(f"Error parsing entity: {e}")
                        continue
                
                self.logger.info(f"Extracted {len(entities)} entities from text")
                return entities[:10]  # Limit to top 10 entities
            
            return []
            
        except openai.error.OpenAIError as e:
            self.log_error(e)
            raise OpenAIException(f"OpenAI API error in entity extraction: {str(e)}")
        except Exception as e:
            self.log_error(e)
            return []  # Return empty list on error for entity extraction
    
    async def health_check(self) -> bool:
        """Check if the knowledge extraction service is healthy"""
        try:
            # Test with a simple extraction
            test_text = "John works at Microsoft in Seattle."
            triples = await self.extract_triples(test_text)
            return len(triples) > 0
        except Exception as e:
            self.log_error(e)
            return False
    
    async def get_service_stats(self) -> Dict[str, Any]:
        """Get service statistics"""
        return {
            "model": self.model,
            "max_tokens": self.max_tokens,
            "temperature": self.temperature,
            "service_type": "openai_gpt4",
            "status": "active"
        }

# Global knowledge extraction service instance
knowledge_extraction_service = KnowledgeExtractionService()