from typing import List, Dict, Any, Optional
import uuid
from datetime import datetime
from src.models.document import DocumentMetadata, TextChunk, ProcessingStatus
from src.database.postgres_client import postgres_client
from src.utils.logging import LoggingMixin
from src.utils.exceptions import PostgresException

class MetadataService(LoggingMixin):
    def __init__(self):
        self.client = postgres_client
    
    async def create_document(self, document: DocumentMetadata) -> str:
        """Create a new document record"""
        try:
            query = """
            INSERT INTO documents (id, filename, file_type, file_size, upload_timestamp, processing_status, metadata)
            VALUES ($1, $2, $3, $4, $5, $6, $7)
            RETURNING id
            """
            
            result = await self.client.execute_query_one(
                query,
                str(document.id),
                document.filename,
                document.file_type,
                document.file_size,
                document.upload_timestamp,
                document.processing_status.value,
                document.metadata or {}
            )
            
            document_id = result["id"] if result else str(document.id)
            self.logger.info(f"Created document record: {document_id}")
            return document_id
            
        except Exception as e:
            self.log_error(e, {"document_id": str(document.id)})
            raise PostgresException(f"Failed to create document: {str(e)}")
    
    async def get_document(self, document_id: str) -> Optional[DocumentMetadata]:
        """Get document by ID"""
        try:
            query = """
            SELECT id, filename, file_type, file_size, upload_timestamp, processing_status, metadata, error_message, created_at, updated_at
            FROM documents
            WHERE id = $1
            """
            
            result = await self.client.execute_query_one(query, document_id)
            
            if result:
                return DocumentMetadata(
                    id=uuid.UUID(result["id"]),
                    filename=result["filename"],
                    file_type=result["file_type"],
                    file_size=result["file_size"],
                    upload_timestamp=result["upload_timestamp"],
                    processing_status=ProcessingStatus(result["processing_status"]),
                    metadata=result["metadata"],
                    error_message=result["error_message"],
                    created_at=result["created_at"],
                    updated_at=result["updated_at"]
                )
            
            return None
            
        except Exception as e:
            self.log_error(e, {"document_id": document_id})
            raise PostgresException(f"Failed to get document: {str(e)}")
    
    async def update_document_status(self, document_id: str, status: ProcessingStatus, error_message: Optional[str] = None) -> bool:
        """Update document processing status"""
        try:
            query = """
            UPDATE documents 
            SET processing_status = $2, error_message = $3, updated_at = NOW()
            WHERE id = $1
            """
            
            await self.client.execute_write_query(query, document_id, status.value, error_message)
            self.logger.info(f"Updated document {document_id} status to {status.value}")
            return True
            
        except Exception as e:
            self.log_error(e, {"document_id": document_id, "status": status.value})
            raise PostgresException(f"Failed to update document status: {str(e)}")
    
    async def list_documents(self, page: int = 1, page_size: int = 20, status: Optional[ProcessingStatus] = None) -> List[DocumentMetadata]:
        """List documents with pagination"""
        try:
            offset = (page - 1) * page_size
            
            if status:
                query = """
                SELECT id, filename, file_type, file_size, upload_timestamp, processing_status, metadata, error_message, created_at, updated_at
                FROM documents
                WHERE processing_status = $1
                ORDER BY upload_timestamp DESC
                LIMIT $2 OFFSET $3
                """
                result = await self.client.execute_query(query, status.value, page_size, offset)
            else:
                query = """
                SELECT id, filename, file_type, file_size, upload_timestamp, processing_status, metadata, error_message, created_at, updated_at
                FROM documents
                ORDER BY upload_timestamp DESC
                LIMIT $1 OFFSET $2
                """
                result = await self.client.execute_query(query, page_size, offset)
            
            documents = []
            for row in result:
                documents.append(DocumentMetadata(
                    id=uuid.UUID(row["id"]),
                    filename=row["filename"],
                    file_type=row["file_type"],
                    file_size=row["file_size"],
                    upload_timestamp=row["upload_timestamp"],
                    processing_status=ProcessingStatus(row["processing_status"]),
                    metadata=row["metadata"],
                    error_message=row["error_message"],
                    created_at=row["created_at"],
                    updated_at=row["updated_at"]
                ))
            
            self.logger.info(f"Retrieved {len(documents)} documents (page {page})")
            return documents
            
        except Exception as e:
            self.log_error(e, {"page": page, "page_size": page_size})
            raise PostgresException(f"Failed to list documents: {str(e)}")
    
    async def create_chunk(self, chunk: TextChunk) -> str:
        """Create a new text chunk record"""
        try:
            query = """
            INSERT INTO chunks (id, document_id, chunk_text, chunk_index, token_count, embedding_vector, metadata)
            VALUES ($1, $2, $3, $4, $5, $6, $7)
            RETURNING id
            """
            
            result = await self.client.execute_query_one(
                query,
                str(chunk.id),
                str(chunk.document_id),
                chunk.chunk_text,
                chunk.chunk_index,
                chunk.token_count,
                chunk.embedding_vector,
                chunk.metadata or {}
            )
            
            chunk_id = result["id"] if result else str(chunk.id)
            self.logger.debug(f"Created chunk record: {chunk_id}")
            return chunk_id
            
        except Exception as e:
            self.log_error(e, {"chunk_id": str(chunk.id)})
            raise PostgresException(f"Failed to create chunk: {str(e)}")
    
    async def get_chunk_by_id(self, chunk_id: str) -> Optional[Dict[str, Any]]:
        """Get a specific chunk by ID"""
        try:
            query = """
            SELECT c.id, c.document_id, c.chunk_text, c.chunk_index, c.token_count, c.embedding_vector, c.metadata, c.created_at,
                   d.filename as document_name
            FROM chunks c
            JOIN documents d ON c.document_id = d.id
            WHERE c.id = $1
            """
            
            result = await self.client.execute_query_one(query, chunk_id)
            
            if result:
                return {
                    "id": result["id"],
                    "document_id": result["document_id"],
                    "document_name": result["document_name"],
                    "chunk_text": result["chunk_text"],
                    "chunk_index": result["chunk_index"],
                    "token_count": result["token_count"],
                    "embedding_vector": result["embedding_vector"],
                    "metadata": result["metadata"] or {},
                    "created_at": result["created_at"]
                }
            
            return None
            
        except Exception as e:
            self.log_error(e, {"chunk_id": chunk_id})
            raise PostgresException(f"Failed to get chunk: {str(e)}")
    
    async def get_all_chunks_with_embeddings(self) -> List[Dict[str, Any]]:
        """Get all chunks with their mock embeddings"""
        try:
            query = """
            SELECT c.id, c.document_id, c.chunk_text, c.chunk_index, c.token_count, c.embedding_vector,
                   d.filename as document_name
            FROM chunks c
            JOIN documents d ON c.document_id = d.id
            WHERE d.processing_status = 'completed'
            ORDER BY c.created_at DESC
            """
            
            rows = await self.client.execute_query(query)
            
            chunks = []
            for row in rows:
                # Generate mock embedding if not stored
                embedding = row["embedding_vector"]
                if not embedding:
                    from src.services.embedding_service import embedding_service
                    embedding = await embedding_service.generate_embedding(row["chunk_text"])
                
                chunks.append({
                    "id": row["id"],
                    "document_id": row["document_id"],
                    "document_name": row["document_name"],
                    "chunk_text": row["chunk_text"],
                    "chunk_index": row["chunk_index"],
                    "token_count": row["token_count"],
                    "embedding": embedding
                })
            
            self.logger.info(f"Retrieved {len(chunks)} chunks with embeddings")
            return chunks
            
        except Exception as e:
            self.log_error(e)
            raise PostgresException(f"Failed to get chunks with embeddings: {str(e)}")
    
    async def get_chunks_by_document(self, document_id: str) -> List[TextChunk]:
        """Get all chunks for a document"""
        try:
            query = """
            SELECT id, document_id, chunk_text, chunk_index, token_count, embedding_vector, metadata, created_at
            FROM chunks
            WHERE document_id = $1
            ORDER BY chunk_index
            """
            
            result = await self.client.execute_query(query, document_id)
            
            chunks = []
            for row in result:
                chunks.append(TextChunk(
                    id=uuid.UUID(row["id"]),
                    document_id=uuid.UUID(row["document_id"]),
                    chunk_text=row["chunk_text"],
                    chunk_index=row["chunk_index"],
                    token_count=row["token_count"],
                    embedding_vector=row["embedding_vector"],
                    metadata=row["metadata"],
                    created_at=row["created_at"]
                ))
            
            self.logger.info(f"Retrieved {len(chunks)} chunks for document {document_id}")
            return chunks
            
        except Exception as e:
            self.log_error(e, {"document_id": document_id})
            raise PostgresException(f"Failed to get chunks by document: {str(e)}")
    
    async def create_graph_chunk_link(self, node_id: str, chunk_id: str, relationship_type: Optional[str] = None, confidence: float = 1.0) -> str:
        """Create a link between graph node and chunk"""
        try:
            link_id = str(uuid.uuid4())
            query = """
            INSERT INTO graph_chunk_links (id, node_id, chunk_id, relationship_type, confidence_score)
            VALUES ($1, $2, $3, $4, $5)
            RETURNING id
            """
            
            result = await self.client.execute_query_one(
                query,
                link_id,
                node_id,
                chunk_id,
                relationship_type,
                confidence
            )
            
            link_id = result["id"] if result else link_id
            self.logger.debug(f"Created graph-chunk link: {link_id}")
            return link_id
            
        except Exception as e:
            self.log_error(e, {"node_id": node_id, "chunk_id": chunk_id})
            raise PostgresException(f"Failed to create graph-chunk link: {str(e)}")
    
    async def get_document_stats(self) -> Dict[str, Any]:
        """Get document processing statistics"""
        try:
            stats_query = """
            SELECT 
                processing_status,
                COUNT(*) as count,
                AVG(file_size) as avg_file_size
            FROM documents
            GROUP BY processing_status
            """
            
            result = await self.client.execute_query(stats_query)
            
            stats = {
                "total_documents": 0,
                "status_breakdown": {},
                "average_file_size": 0
            }
            
            total_size = 0
            total_count = 0
            
            for row in result:
                status = row["processing_status"]
                count = row["count"]
                avg_size = row["avg_file_size"] or 0
                
                stats["status_breakdown"][status] = count
                stats["total_documents"] += count
                total_size += avg_size * count
                total_count += count
            
            if total_count > 0:
                stats["average_file_size"] = total_size / total_count
            
            # Get chunk statistics
            chunk_stats_query = """
            SELECT 
                COUNT(*) as total_chunks,
                AVG(token_count) as avg_token_count,
                AVG(LENGTH(chunk_text)) as avg_chunk_length
            FROM chunks
            """
            
            chunk_result = await self.client.execute_query(chunk_stats_query)
            if chunk_result:
                chunk_data = chunk_result[0]
                stats.update({
                    "total_chunks": chunk_data["total_chunks"],
                    "average_token_count": chunk_data["avg_token_count"] or 0,
                    "average_chunk_length": chunk_data["avg_chunk_length"] or 0
                })
            
            return stats
            
        except Exception as e:
            self.log_error(e)
            raise PostgresException(f"Failed to get document stats: {str(e)}")
    
    async def health_check(self) -> bool:
        """Check if the metadata service is healthy"""
        try:
            result = await self.client.test_connection()
            return result
        except Exception as e:
            self.log_error(e)
            return False

# Global metadata service instance
metadata_service = MetadataService()