from typing import List, Dict, Any, Optional
from src.models.graph import Triple, Entity, GraphNode, GraphPath, GraphStats
from src.database.neo4j_client import neo4j_client
from src.utils.logging import LoggingMixin
from src.utils.exceptions import Neo4jException
import uuid

class GraphService(LoggingMixin):
    def __init__(self):
        self.client = neo4j_client
    
    async def store_triples(self, triples: List[Triple], chunk_id: str) -> bool:
        """Store extracted triples in the graph database"""
        
        try:
            for triple in triples:
                # Create or merge entities and relationships
                query = """
                MERGE (s:Entity {name: $subject_name})
                SET s.type = $subject_type,
                    s.updated_at = datetime(),
                    s.confidence = CASE WHEN s.confidence IS NULL THEN $subject_confidence ELSE (s.confidence + $subject_confidence) / 2 END
                
                MERGE (o:Entity {name: $object_name})
                SET o.type = $object_type,
                    o.updated_at = datetime(),
                    o.confidence = CASE WHEN o.confidence IS NULL THEN $object_confidence ELSE (o.confidence + $object_confidence) / 2 END
                
                MERGE (c:Chunk {id: $chunk_id})
                
                MERGE (s)-[r:RELATES_TO {
                    type: $relationship,
                    confidence: $confidence,
                    chunk_id: $chunk_id,
                    context: $context
                }]->(o)
                
                MERGE (c)-[:MENTIONS]->(s)
                MERGE (c)-[:MENTIONS]->(o)
                
                RETURN s.name, r.type, o.name
                """
                
                await self.client.execute_write_query(query, {
                    "subject_name": triple.subject.name,
                    "subject_type": triple.subject.type.value,
                    "subject_confidence": triple.subject.confidence,
                    "object_name": triple.object.name,
                    "object_type": triple.object.type.value,
                    "object_confidence": triple.object.confidence,
                    "relationship": triple.relationship,
                    "confidence": triple.confidence,
                    "chunk_id": chunk_id,
                    "context": triple.context or ""
                })
            
            self.logger.info(f"Stored {len(triples)} triples for chunk {chunk_id}")
            return True
            
        except Exception as e:
            self.log_error(e, {"chunk_id": chunk_id, "triples_count": len(triples)})
            raise Neo4jException(f"Failed to store triples: {str(e)}")
    
    async def find_entities_by_names(self, entity_names: List[str]) -> List[GraphNode]:
        """Find entities by their names"""
        
        try:
            query = """
            MATCH (e:Entity)
            WHERE e.name IN $entity_names
            OPTIONAL MATCH (e)-[r]->(related:Entity)
            RETURN e, collect({
                type: type(r),
                target: related.name,
                confidence: r.confidence,
                relationship_id: id(r)
            }) as relationships
            """
            
            result = await self.client.execute_query(query, {"entity_names": entity_names})
            
            nodes = []
            for record in result:
                entity = record["e"]
                relationships = record["relationships"]
                
                # Filter out null relationships
                valid_relationships = [rel for rel in relationships if rel["type"] is not None]
                
                node = GraphNode(
                    id=entity["name"],
                    name=entity["name"],
                    type=entity.get("type", "other"),
                    properties={
                        "confidence": entity.get("confidence", 1.0),
                        "updated_at": str(entity.get("updated_at", "")),
                        "relationship_count": len(valid_relationships)
                    },
                    relationships=valid_relationships
                )
                nodes.append(node)
            
            self.logger.info(f"Found {len(nodes)} entities from {len(entity_names)} requested")
            return nodes
            
        except Exception as e:
            self.log_error(e, {"entity_names": entity_names})
            raise Neo4jException(f"Failed to find entities: {str(e)}")
    
    async def explore_subgraph(self, start_entities: List[str], 
                              max_depth: int = 2, 
                              relationship_types: Optional[List[str]] = None) -> Dict[str, Any]:
        """Explore subgraph starting from given entities"""
        
        try:
            # Build relationship filter
            rel_filter = ""
            if relationship_types:
                rel_filter = f"AND type(r) IN {relationship_types}"
            
            query = f"""
            MATCH path = (start:Entity)-[r*1..{max_depth}]-(end:Entity)
            WHERE start.name IN $start_entities {rel_filter}
            WITH path, relationships(path) as rels, nodes(path) as nodes
            RETURN 
                [n IN nodes | {{
                    name: n.name, 
                    type: n.type,
                    confidence: n.confidence,
                    properties: properties(n)
                }}] as path_nodes,
                [r IN rels | {{
                    type: type(r),
                    confidence: r.confidence,
                    source: startNode(r).name,
                    target: endNode(r).name,
                    context: r.context
                }}] as path_relationships,
                length(path) as path_length
            ORDER BY path_length
            LIMIT 100
            """
            
            result = await self.client.execute_query(query, {"start_entities": start_entities})
            
            # Process results into subgraph structure
            nodes = {}
            relationships = []
            
            for record in result:
                path_nodes = record["path_nodes"]
                path_relationships = record["path_relationships"]
                
                # Add nodes
                for node in path_nodes:
                    nodes[node["name"]] = node
                
                # Add relationships
                relationships.extend(path_relationships)
            
            # Remove duplicate relationships
            unique_relationships = []
            seen_rels = set()
            for rel in relationships:
                rel_key = (rel["source"], rel["type"], rel["target"])
                if rel_key not in seen_rels:
                    unique_relationships.append(rel)
                    seen_rels.add(rel_key)
            
            subgraph = {
                "nodes": list(nodes.values()),
                "relationships": unique_relationships,
                "node_count": len(nodes),
                "relationship_count": len(unique_relationships)
            }
            
            self.logger.info(f"Explored subgraph: {len(nodes)} nodes, {len(unique_relationships)} relationships")
            return subgraph
            
        except Exception as e:
            self.log_error(e, {"start_entities": start_entities, "max_depth": max_depth})
            raise Neo4jException(f"Failed to explore subgraph: {str(e)}")
    
    async def get_entity_context_chunks(self, entity_names: List[str]) -> List[str]:
        """Get all chunk IDs that mention the given entities"""
        
        try:
            query = """
            MATCH (e:Entity)-[:MENTIONS]-(c:Chunk)
            WHERE e.name IN $entity_names
            RETURN DISTINCT c.id as chunk_id
            """
            
            result = await self.client.execute_query(query, {"entity_names": entity_names})
            chunk_ids = [record["chunk_id"] for record in result]
            
            self.logger.info(f"Found {len(chunk_ids)} chunks mentioning {len(entity_names)} entities")
            return chunk_ids
            
        except Exception as e:
            self.log_error(e, {"entity_names": entity_names})
            raise Neo4jException(f"Failed to get entity context chunks: {str(e)}")
    
    async def get_graph_stats(self) -> GraphStats:
        """Get comprehensive graph statistics"""
        
        try:
            stats_query = """
            MATCH (n:Entity)
            OPTIONAL MATCH (n)-[r:RELATES_TO]-()
            RETURN 
                count(DISTINCT n) as node_count,
                count(DISTINCT r) as relationship_count,
                collect(DISTINCT n.type) as node_types,
                collect(DISTINCT r.type) as relationship_types,
                avg(size((n)-[:RELATES_TO]-())) as avg_degree
            """
            
            result = await self.client.execute_query(stats_query)
            
            if result:
                data = result[0]
                
                # Count node types
                node_type_counts = {}
                for node_type in data["node_types"]:
                    if node_type:
                        type_count_query = """
                        MATCH (n:Entity {type: $node_type})
                        RETURN count(n) as count
                        """
                        type_result = await self.client.execute_query(type_count_query, {"node_type": node_type})
                        node_type_counts[node_type] = type_result[0]["count"] if type_result else 0
                
                # Count relationship types
                rel_type_counts = {}
                for rel_type in data["relationship_types"]:
                    if rel_type:
                        type_count_query = """
                        MATCH ()-[r:RELATES_TO {type: $rel_type}]-()
                        RETURN count(r) as count
                        """
                        type_result = await self.client.execute_query(type_count_query, {"rel_type": rel_type})
                        rel_type_counts[rel_type] = type_result[0]["count"] if type_result else 0
                
                # Get connected components count (simplified)
                components_query = """
                MATCH (n:Entity)
                WHERE NOT (n)-[:RELATES_TO]-()
                RETURN count(n) as isolated_nodes
                """
                components_result = await self.client.execute_query(components_query)
                isolated_nodes = components_result[0]["isolated_nodes"] if components_result else 0
                
                return GraphStats(
                    total_nodes=data["node_count"],
                    total_relationships=data["relationship_count"],
                    node_types=node_type_counts,
                    relationship_types=rel_type_counts,
                    average_degree=float(data["avg_degree"] or 0),
                    connected_components=1 if data["node_count"] > isolated_nodes else 0
                )
            
            return GraphStats(
                total_nodes=0,
                total_relationships=0,
                node_types={},
                relationship_types={},
                average_degree=0.0,
                connected_components=0
            )
            
        except Exception as e:
            self.log_error(e)
            raise Neo4jException(f"Failed to get graph stats: {str(e)}")
    
    async def find_shortest_path(self, start_entity: str, end_entity: str, max_length: int = 5) -> Optional[GraphPath]:
        """Find shortest path between two entities"""
        
        try:
            query = f"""
            MATCH path = shortestPath((start:Entity {{name: $start_entity}})-[*1..{max_length}]-(end:Entity {{name: $end_entity}}))
            RETURN path, length(path) as path_length
            """
            
            result = await self.client.execute_query(query, {
                "start_entity": start_entity,
                "end_entity": end_entity
            })
            
            if result:
                # Process path result
                path_data = result[0]
                # This would need more complex processing to extract nodes and relationships
                # For now, return a simplified version
                return GraphPath(
                    nodes=[],
                    relationships=[],
                    path_length=path_data["path_length"],
                    relevance_score=1.0 / (path_data["path_length"] + 1)
                )
            
            return None
            
        except Exception as e:
            self.log_error(e, {"start_entity": start_entity, "end_entity": end_entity})
            raise Neo4jException(f"Failed to find shortest path: {str(e)}")
    
    async def health_check(self) -> bool:
        """Check if the graph service is healthy"""
        try:
            stats = await self.get_graph_stats()
            return stats.total_nodes >= 0  # Basic health check
        except Exception as e:
            self.log_error(e)
            return False

# Global graph service instance
graph_service = GraphService()