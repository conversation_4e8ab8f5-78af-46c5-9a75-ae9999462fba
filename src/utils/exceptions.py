"""Custom exceptions for Project Synapse"""

class SynapseException(Exception):
    """Base exception for all Synapse-related errors"""
    def __init__(self, message: str, error_code: str = None):
        self.message = message
        self.error_code = error_code
        super().__init__(self.message)

class DatabaseException(SynapseException):
    """Database-related exceptions"""
    pass

class Neo4jException(DatabaseException):
    """Neo4j-specific exceptions"""
    pass

class PostgresException(DatabaseException):
    """PostgreSQL-specific exceptions"""
    pass

class RedisException(DatabaseException):
    """Redis-specific exceptions"""
    pass

class ServiceException(SynapseException):
    """Service-related exceptions"""
    pass

class KnowledgeExtractionException(ServiceException):
    """Knowledge extraction service exceptions"""
    pass

class EmbeddingServiceException(ServiceException):
    """Embedding service exceptions"""
    pass

class PreprocessingException(ServiceException):
    """Document preprocessing exceptions"""
    pass

class QueryProcessingException(ServiceException):
    """Query processing exceptions"""
    pass

class AuthenticationException(SynapseException):
    """Authentication-related exceptions"""
    pass

class AuthorizationException(SynapseException):
    """Authorization-related exceptions"""
    pass

class ValidationException(SynapseException):
    """Data validation exceptions"""
    pass

class FileProcessingException(SynapseException):
    """File processing exceptions"""
    pass

class OpenAIException(ServiceException):
    """OpenAI API-related exceptions"""
    pass

class CeleryTaskException(SynapseException):
    """Celery task-related exceptions"""
    pass