import redis.asyncio as redis
from typing import Any, Optional, Dict, List
from src.utils.config import settings
from src.utils.logging import LoggingMixin
from src.utils.exceptions import RedisException
import json
import pickle

class RedisClient(LoggingMixin):
    def __init__(self):
        self.client = None
        
    async def connect(self):
        """Initialize Redis connection"""
        try:
            self.client = redis.from_url(
                settings.REDIS_URL,
                encoding="utf-8",
                decode_responses=False  # We'll handle encoding manually
            )
            # Test connection
            await self.test_connection()
            self.logger.info("Redis connection established")
        except Exception as e:
            self.logger.error("Failed to connect to Redis", error=str(e))
            raise RedisException(f"Failed to connect to Redis: {str(e)}")
    
    async def close(self):
        """Close Redis connection"""
        if self.client:
            await self.client.close()
            self.logger.info("Redis connection closed")
    
    async def test_connection(self) -> bool:
        """Test Redis connection"""
        try:
            result = await self.client.ping()
            return result
        except Exception as e:
            raise RedisException(f"Redis connection test failed: {str(e)}")
    
    async def set(self, key: str, value: Any, expire: Optional[int] = None) -> bool:
        """Set a key-value pair"""
        try:
            if isinstance(value, (dict, list)):
                value = json.dumps(value)
            elif not isinstance(value, (str, bytes, int, float)):
                value = pickle.dumps(value)
            
            result = await self.client.set(key, value, ex=expire)
            return result
        except Exception as e:
            self.logger.error(f"Failed to set key: {key}", error=str(e))
            raise RedisException(f"Failed to set key: {str(e)}")
    
    async def get(self, key: str) -> Optional[Any]:
        """Get a value by key"""
        try:
            value = await self.client.get(key)
            if value is None:
                return None
            
            # Try to decode as JSON first
            try:
                return json.loads(value)
            except (json.JSONDecodeError, TypeError):
                pass
            
            # Try to decode as pickle
            try:
                return pickle.loads(value)
            except (pickle.PickleError, TypeError):
                pass
            
            # Return as string
            return value.decode('utf-8') if isinstance(value, bytes) else value
        except Exception as e:
            self.logger.error(f"Failed to get key: {key}", error=str(e))
            raise RedisException(f"Failed to get key: {str(e)}")
    
    async def delete(self, key: str) -> bool:
        """Delete a key"""
        try:
            result = await self.client.delete(key)
            return result > 0
        except Exception as e:
            self.logger.error(f"Failed to delete key: {key}", error=str(e))
            raise RedisException(f"Failed to delete key: {str(e)}")
    
    async def exists(self, key: str) -> bool:
        """Check if key exists"""
        try:
            result = await self.client.exists(key)
            return result > 0
        except Exception as e:
            self.logger.error(f"Failed to check key existence: {key}", error=str(e))
            raise RedisException(f"Failed to check key existence: {str(e)}")
    
    async def expire(self, key: str, seconds: int) -> bool:
        """Set expiration for a key"""
        try:
            result = await self.client.expire(key, seconds)
            return result
        except Exception as e:
            self.logger.error(f"Failed to set expiration for key: {key}", error=str(e))
            raise RedisException(f"Failed to set expiration: {str(e)}")
    
    async def hset(self, name: str, mapping: Dict[str, Any]) -> int:
        """Set hash fields"""
        try:
            # Convert values to strings/bytes
            processed_mapping = {}
            for k, v in mapping.items():
                if isinstance(v, (dict, list)):
                    processed_mapping[k] = json.dumps(v)
                elif not isinstance(v, (str, bytes, int, float)):
                    processed_mapping[k] = pickle.dumps(v)
                else:
                    processed_mapping[k] = v
            
            result = await self.client.hset(name, mapping=processed_mapping)
            return result
        except Exception as e:
            self.logger.error(f"Failed to set hash: {name}", error=str(e))
            raise RedisException(f"Failed to set hash: {str(e)}")
    
    async def hget(self, name: str, key: str) -> Optional[Any]:
        """Get hash field value"""
        try:
            value = await self.client.hget(name, key)
            if value is None:
                return None
            
            # Try to decode as JSON first
            try:
                return json.loads(value)
            except (json.JSONDecodeError, TypeError):
                pass
            
            # Try to decode as pickle
            try:
                return pickle.loads(value)
            except (pickle.PickleError, TypeError):
                pass
            
            # Return as string
            return value.decode('utf-8') if isinstance(value, bytes) else value
        except Exception as e:
            self.logger.error(f"Failed to get hash field: {name}.{key}", error=str(e))
            raise RedisException(f"Failed to get hash field: {str(e)}")
    
    async def hgetall(self, name: str) -> Dict[str, Any]:
        """Get all hash fields"""
        try:
            result = await self.client.hgetall(name)
            processed_result = {}
            
            for k, v in result.items():
                key = k.decode('utf-8') if isinstance(k, bytes) else k
                
                # Try to decode value as JSON first
                try:
                    processed_result[key] = json.loads(v)
                    continue
                except (json.JSONDecodeError, TypeError):
                    pass
                
                # Try to decode as pickle
                try:
                    processed_result[key] = pickle.loads(v)
                    continue
                except (pickle.PickleError, TypeError):
                    pass
                
                # Return as string
                processed_result[key] = v.decode('utf-8') if isinstance(v, bytes) else v
            
            return processed_result
        except Exception as e:
            self.logger.error(f"Failed to get all hash fields: {name}", error=str(e))
            raise RedisException(f"Failed to get all hash fields: {str(e)}")
    
    async def lpush(self, name: str, *values) -> int:
        """Push values to the left of a list"""
        try:
            processed_values = []
            for value in values:
                if isinstance(value, (dict, list)):
                    processed_values.append(json.dumps(value))
                elif not isinstance(value, (str, bytes, int, float)):
                    processed_values.append(pickle.dumps(value))
                else:
                    processed_values.append(value)
            
            result = await self.client.lpush(name, *processed_values)
            return result
        except Exception as e:
            self.logger.error(f"Failed to push to list: {name}", error=str(e))
            raise RedisException(f"Failed to push to list: {str(e)}")
    
    async def rpop(self, name: str) -> Optional[Any]:
        """Pop value from the right of a list"""
        try:
            value = await self.client.rpop(name)
            if value is None:
                return None
            
            # Try to decode as JSON first
            try:
                return json.loads(value)
            except (json.JSONDecodeError, TypeError):
                pass
            
            # Try to decode as pickle
            try:
                return pickle.loads(value)
            except (pickle.PickleError, TypeError):
                pass
            
            # Return as string
            return value.decode('utf-8') if isinstance(value, bytes) else value
        except Exception as e:
            self.logger.error(f"Failed to pop from list: {name}", error=str(e))
            raise RedisException(f"Failed to pop from list: {str(e)}")

# Global Redis client instance
redis_client = RedisClient()