from neo4j import GraphDatabase
from typing import List, Dict, Any, Optional
from src.utils.config import settings
from src.utils.logging import LoggingMixin
from src.utils.exceptions import Neo4jException
import asyncio
from concurrent.futures import ThreadPoolExecutor

class Neo4jClient(LoggingMixin):
    def __init__(self):
        self.driver = None
        self.executor = ThreadPoolExecutor(max_workers=4)
        
    async def connect(self):
        """Initialize Neo4j connection"""
        try:
            self.driver = GraphDatabase.driver(
                settings.NEO4J_URI,
                auth=(settings.NEO4J_USER, settings.NEO4J_PASSWORD)
            )
            # Test connection
            await self.test_connection()
            self.logger.info("Neo4j connection established")
        except Exception as e:
            self.logger.error("Failed to connect to Neo4j", error=str(e))
            raise Neo4jException(f"Failed to connect to Neo4j: {str(e)}")
    
    async def close(self):
        """Close Neo4j connection"""
        if self.driver:
            self.driver.close()
            self.logger.info("Neo4j connection closed")
    
    async def test_connection(self) -> bool:
        """Test Neo4j connection"""
        try:
            def _test():
                with self.driver.session() as session:
                    result = session.run("RETURN 1 as test")
                    return result.single()["test"] == 1
            
            loop = asyncio.get_event_loop()
            result = await loop.run_in_executor(self.executor, _test)
            return result
        except Exception as e:
            raise Neo4jException(f"Neo4j connection test failed: {str(e)}")
    
    async def create_constraints(self):
        """Create necessary constraints and indexes"""
        constraints = [
            "CREATE CONSTRAINT entity_name IF NOT EXISTS FOR (e:Entity) REQUIRE e.name IS UNIQUE",
            "CREATE CONSTRAINT document_id IF NOT EXISTS FOR (d:Document) REQUIRE d.id IS UNIQUE",
            "CREATE CONSTRAINT chunk_id IF NOT EXISTS FOR (c:Chunk) REQUIRE c.id IS UNIQUE"
        ]
        
        indexes = [
            "CREATE INDEX entity_type IF NOT EXISTS FOR (e:Entity) ON (e.type)",
            "CREATE INDEX entity_created IF NOT EXISTS FOR (e:Entity) ON (e.created_at)",
            "CREATE INDEX chunk_document IF NOT EXISTS FOR (c:Chunk) ON (c.document_id)",
            "CREATE INDEX relationship_type IF NOT EXISTS FOR ()-[r:RELATES_TO]-() ON (r.type)",
            "CREATE INDEX relationship_confidence IF NOT EXISTS FOR ()-[r:RELATES_TO]-() ON (r.confidence)"
        ]
        
        def _create_constraints_and_indexes():
            with self.driver.session() as session:
                for constraint in constraints:
                    try:
                        session.run(constraint)
                        self.logger.debug(f"Created constraint: {constraint}")
                    except Exception as e:
                        self.logger.warning(f"Constraint may already exist: {e}")
                
                for index in indexes:
                    try:
                        session.run(index)
                        self.logger.debug(f"Created index: {index}")
                    except Exception as e:
                        self.logger.warning(f"Index may already exist: {e}")
        
        try:
            loop = asyncio.get_event_loop()
            await loop.run_in_executor(self.executor, _create_constraints_and_indexes)
            self.logger.info("Neo4j constraints and indexes created")
        except Exception as e:
            raise Neo4jException(f"Failed to create constraints/indexes: {str(e)}")
    
    async def execute_query(self, query: str, parameters: Dict[str, Any] = None) -> List[Dict[str, Any]]:
        """Execute a Cypher query"""
        def _execute():
            with self.driver.session() as session:
                result = session.run(query, parameters or {})
                return [record.data() for record in result]
        
        try:
            loop = asyncio.get_event_loop()
            return await loop.run_in_executor(self.executor, _execute)
        except Exception as e:
            self.logger.error(f"Query execution failed: {query}", error=str(e))
            raise Neo4jException(f"Query execution failed: {str(e)}")
    
    async def execute_write_query(self, query: str, parameters: Dict[str, Any] = None) -> Dict[str, Any]:
        """Execute a write Cypher query"""
        def _execute():
            with self.driver.session() as session:
                result = session.run(query, parameters or {})
                summary = result.consume()
                return {
                    "nodes_created": summary.counters.nodes_created,
                    "relationships_created": summary.counters.relationships_created,
                    "properties_set": summary.counters.properties_set
                }
        
        try:
            loop = asyncio.get_event_loop()
            return await loop.run_in_executor(self.executor, _execute)
        except Exception as e:
            self.logger.error(f"Write query execution failed: {query}", error=str(e))
            raise Neo4jException(f"Write query execution failed: {str(e)}")
    
    async def get_graph_stats(self) -> Dict[str, Any]:
        """Get graph statistics"""
        stats_query = """
        MATCH (n)
        OPTIONAL MATCH (n)-[r]-()
        RETURN 
            count(DISTINCT n) as node_count,
            count(DISTINCT r) as relationship_count,
            collect(DISTINCT labels(n)) as node_labels,
            collect(DISTINCT type(r)) as relationship_types
        """
        
        try:
            result = await self.execute_query(stats_query)
            if result:
                data = result[0]
                return {
                    "nodes": data["node_count"],
                    "relationships": data["relationship_count"],
                    "node_types": [label for labels in data["node_labels"] for label in labels if labels],
                    "relationship_types": [rt for rt in data["relationship_types"] if rt]
                }
            return {"nodes": 0, "relationships": 0, "node_types": [], "relationship_types": []}
        except Exception as e:
            self.logger.error("Failed to get graph stats", error=str(e))
            raise Neo4jException(f"Failed to get graph stats: {str(e)}")

# Global Neo4j client instance
neo4j_client = Neo4jClient()