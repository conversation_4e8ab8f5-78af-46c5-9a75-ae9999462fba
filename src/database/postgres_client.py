import asyncpg
from typing import List, Dict, Any, Optional
from src.utils.config import settings
from src.utils.logging import LoggingMixin
from src.utils.exceptions import PostgresException
import asyncio

class PostgresClient(LoggingMixin):
    def __init__(self):
        self.pool = None
        
    async def connect(self):
        """Initialize PostgreSQL connection pool"""
        try:
            self.pool = await asyncpg.create_pool(
                settings.POSTGRES_URL,
                min_size=2,
                max_size=10,
                command_timeout=60
            )
            # Test connection
            await self.test_connection()
            self.logger.info("PostgreSQL connection pool established")
        except Exception as e:
            self.logger.error("Failed to connect to PostgreSQL", error=str(e))
            raise PostgresException(f"Failed to connect to PostgreSQL: {str(e)}")
    
    async def close(self):
        """Close PostgreSQL connection pool"""
        if self.pool:
            await self.pool.close()
            self.logger.info("PostgreSQL connection pool closed")
    
    async def test_connection(self) -> bool:
        """Test PostgreSQL connection"""
        try:
            async with self.pool.acquire() as conn:
                result = await conn.fetchval("SELECT 1")
                return result == 1
        except Exception as e:
            raise PostgresException(f"PostgreSQL connection test failed: {str(e)}")
    
    async def execute_query(self, query: str, *args) -> List[Dict[str, Any]]:
        """Execute a SELECT query"""
        try:
            async with self.pool.acquire() as conn:
                rows = await conn.fetch(query, *args)
                return [dict(row) for row in rows]
        except Exception as e:
            self.logger.error(f"Query execution failed: {query}", error=str(e))
            raise PostgresException(f"Query execution failed: {str(e)}")
    
    async def execute_query_one(self, query: str, *args) -> Optional[Dict[str, Any]]:
        """Execute a SELECT query and return one row"""
        try:
            async with self.pool.acquire() as conn:
                row = await conn.fetchrow(query, *args)
                return dict(row) if row else None
        except Exception as e:
            self.logger.error(f"Query execution failed: {query}", error=str(e))
            raise PostgresException(f"Query execution failed: {str(e)}")
    
    async def execute_write_query(self, query: str, *args) -> str:
        """Execute an INSERT/UPDATE/DELETE query"""
        try:
            async with self.pool.acquire() as conn:
                result = await conn.execute(query, *args)
                return result
        except Exception as e:
            self.logger.error(f"Write query execution failed: {query}", error=str(e))
            raise PostgresException(f"Write query execution failed: {str(e)}")
    
    async def execute_transaction(self, queries: List[tuple]) -> List[Any]:
        """Execute multiple queries in a transaction"""
        try:
            async with self.pool.acquire() as conn:
                async with conn.transaction():
                    results = []
                    for query, args in queries:
                        if query.strip().upper().startswith('SELECT'):
                            result = await conn.fetch(query, *args)
                            results.append([dict(row) for row in result])
                        else:
                            result = await conn.execute(query, *args)
                            results.append(result)
                    return results
        except Exception as e:
            self.logger.error("Transaction execution failed", error=str(e))
            raise PostgresException(f"Transaction execution failed: {str(e)}")
    
    async def get_table_stats(self) -> Dict[str, Any]:
        """Get database table statistics"""
        stats_query = """
        SELECT 
            schemaname,
            tablename,
            n_tup_ins as inserts,
            n_tup_upd as updates,
            n_tup_del as deletes,
            n_live_tup as live_tuples,
            n_dead_tup as dead_tuples
        FROM pg_stat_user_tables
        ORDER BY tablename;
        """
        
        try:
            return await self.execute_query(stats_query)
        except Exception as e:
            self.logger.error("Failed to get table stats", error=str(e))
            raise PostgresException(f"Failed to get table stats: {str(e)}")

# Global PostgreSQL client instance
postgres_client = PostgresClient()