from fastapi import APIRouter, HTTPException
from src.database.neo4j_client import neo4j_client
from src.database.postgres_client import postgres_client
from src.database.redis_client import redis_client
from src.services.embedding_service import embedding_service
from src.services.graph_service import graph_service
from src.services.metadata_service import metadata_service
from src.services.knowledge_extraction import knowledge_extraction_service
from src.utils.logging import get_logger

router = APIRouter()
logger = get_logger(__name__)

@router.get("/health")
async def health_check():
    """Comprehensive health check for all services"""
    
    health_status = {
        "status": "healthy",
        "services": {},
        "timestamp": "2024-01-01T00:00:00Z"  # This would be dynamic in real implementation
    }
    
    try:
        # Check database connections
        try:
            neo4j_health = await neo4j_client.test_connection()
            health_status["services"]["neo4j"] = "healthy" if neo4j_health else "unhealthy"
        except Exception as e:
            health_status["services"]["neo4j"] = "unhealthy"
            logger.error(f"Neo4j health check failed: {e}")
        
        try:
            postgres_health = await postgres_client.test_connection()
            health_status["services"]["postgres"] = "healthy" if postgres_health else "unhealthy"
        except Exception as e:
            health_status["services"]["postgres"] = "unhealthy"
            logger.error(f"PostgreSQL health check failed: {e}")
        
        try:
            redis_health = await redis_client.test_connection()
            health_status["services"]["redis"] = "healthy" if redis_health else "unhealthy"
        except Exception as e:
            health_status["services"]["redis"] = "unhealthy"
            logger.error(f"Redis health check failed: {e}")
        
        # Check application services
        try:
            embedding_health = await embedding_service.health_check()
            health_status["services"]["embedding_service"] = "healthy" if embedding_health else "unhealthy"
        except Exception as e:
            health_status["services"]["embedding_service"] = "unhealthy"
            logger.error(f"Embedding service health check failed: {e}")
        
        try:
            graph_health = await graph_service.health_check()
            health_status["services"]["graph_service"] = "healthy" if graph_health else "unhealthy"
        except Exception as e:
            health_status["services"]["graph_service"] = "unhealthy"
            logger.error(f"Graph service health check failed: {e}")
        
        try:
            metadata_health = await metadata_service.health_check()
            health_status["services"]["metadata_service"] = "healthy" if metadata_health else "unhealthy"
        except Exception as e:
            health_status["services"]["metadata_service"] = "unhealthy"
            logger.error(f"Metadata service health check failed: {e}")
        
        try:
            knowledge_health = await knowledge_extraction_service.health_check()
            health_status["services"]["knowledge_extraction"] = "healthy" if knowledge_health else "unhealthy"
        except Exception as e:
            health_status["services"]["knowledge_extraction"] = "unhealthy"
            logger.error(f"Knowledge extraction service health check failed: {e}")
        
        # Determine overall status
        unhealthy_services = [k for k, v in health_status["services"].items() if v == "unhealthy"]
        if unhealthy_services:
            health_status["status"] = "degraded" if len(unhealthy_services) < len(health_status["services"]) else "unhealthy"
            health_status["unhealthy_services"] = unhealthy_services
        
        return health_status
        
    except Exception as e:
        logger.error(f"Health check failed: {str(e)}")
        raise HTTPException(
            status_code=503,
            detail="Health check failed"
        )

@router.get("/health/detailed")
async def detailed_health_check():
    """Detailed health check with statistics"""
    
    try:
        # Get basic health status
        basic_health = await health_check()
        
        # Add detailed statistics
        detailed_status = {
            **basic_health,
            "statistics": {}
        }
        
        # Graph statistics
        try:
            graph_stats = await graph_service.get_graph_stats()
            detailed_status["statistics"]["graph"] = {
                "total_nodes": graph_stats.total_nodes,
                "total_relationships": graph_stats.total_relationships,
                "average_degree": graph_stats.average_degree,
                "node_types": len(graph_stats.node_types),
                "relationship_types": len(graph_stats.relationship_types)
            }
        except Exception as e:
            logger.error(f"Failed to get graph stats: {e}")
            detailed_status["statistics"]["graph"] = {"error": "Failed to retrieve stats"}
        
        # Document statistics
        try:
            doc_stats = await metadata_service.get_document_stats()
            detailed_status["statistics"]["documents"] = doc_stats
        except Exception as e:
            logger.error(f"Failed to get document stats: {e}")
            detailed_status["statistics"]["documents"] = {"error": "Failed to retrieve stats"}
        
        # Embedding service statistics
        try:
            embedding_stats = await embedding_service.get_embedding_stats()
            detailed_status["statistics"]["embeddings"] = embedding_stats
        except Exception as e:
            logger.error(f"Failed to get embedding stats: {e}")
            detailed_status["statistics"]["embeddings"] = {"error": "Failed to retrieve stats"}
        
        return detailed_status
        
    except Exception as e:
        logger.error(f"Detailed health check failed: {str(e)}")
        raise HTTPException(
            status_code=503,
            detail="Detailed health check failed"
        )

@router.get("/health/ready")
async def readiness_check():
    """Readiness check for Kubernetes"""
    
    try:
        # Check if all critical services are ready
        neo4j_ready = await neo4j_client.test_connection()
        postgres_ready = await postgres_client.test_connection()
        
        if neo4j_ready and postgres_ready:
            return {"status": "ready"}
        else:
            raise HTTPException(
                status_code=503,
                detail="Service not ready"
            )
            
    except Exception as e:
        logger.error(f"Readiness check failed: {str(e)}")
        raise HTTPException(
            status_code=503,
            detail="Service not ready"
        )

@router.get("/health/live")
async def liveness_check():
    """Liveness check for Kubernetes"""
    
    try:
        # Basic liveness check - just return OK if the service is running
        return {"status": "alive"}
        
    except Exception as e:
        logger.error(f"Liveness check failed: {str(e)}")
        raise HTTPException(
            status_code=503,
            detail="Service not alive"
        )