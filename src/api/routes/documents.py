from fastapi import APIRouter, HTTPException, UploadFile, File, Form
from typing import List, Optional
import uuid
import os
from src.models.document import DocumentMetadata, DocumentResponse, DocumentListResponse, DocumentUploadResponse, ProcessingStatus
from src.services.metadata_service import metadata_service
from src.utils.config import settings
from src.utils.logging import get_logger

router = APIRouter()
logger = get_logger(__name__)

@router.post("/documents/upload", response_model=DocumentUploadResponse)
async def upload_document(
    file: UploadFile = File(...),
    metadata: Optional[str] = Form(None)
):
    """Upload a document for processing"""
    
    try:
        # Validate file type
        if not file.filename:
            raise HTTPException(status_code=400, detail="No filename provided")
        
        file_extension = file.filename.split('.')[-1].lower()
        if file_extension not in settings.SUPPORTED_FORMATS:
            raise HTTPException(
                status_code=400, 
                detail=f"Unsupported file format. Supported formats: {settings.SUPPORTED_FORMATS}"
            )
        
        # Check file size
        file_content = await file.read()
        if len(file_content) > settings.MAX_FILE_SIZE:
            raise HTTPException(
                status_code=400,
                detail=f"File too large. Maximum size: {settings.MAX_FILE_SIZE} bytes"
            )
        
        # Create document metadata
        document_id = uuid.uuid4()
        document_metadata = DocumentMetadata(
            id=document_id,
            filename=file.filename,
            file_type=file_extension,
            file_size=len(file_content),
            processing_status=ProcessingStatus.PENDING,
            metadata={"original_metadata": metadata} if metadata else None
        )
        
        # Save document metadata
        await metadata_service.create_document(document_metadata)
        
        # Save file to disk (in production, this would be cloud storage)
        upload_dir = "uploads"
        os.makedirs(upload_dir, exist_ok=True)
        file_path = os.path.join(upload_dir, f"{document_id}_{file.filename}")
        
        with open(file_path, "wb") as f:
            f.write(file_content)
        
        # TODO: Queue document for processing with Celery
        # await queue_document_processing(document_id, file_path)
        
        logger.info(f"Document uploaded successfully: {document_id}")
        
        return DocumentUploadResponse(
            document_id=document_id,
            filename=file.filename,
            status=ProcessingStatus.PENDING,
            message="Document uploaded successfully and queued for processing"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error uploading document: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error uploading document: {str(e)}"
        )

@router.get("/documents/{document_id}", response_model=DocumentResponse)
async def get_document(document_id: str):
    """Get document by ID"""
    
    try:
        document = await metadata_service.get_document(document_id)
        
        if not document:
            raise HTTPException(status_code=404, detail="Document not found")
        
        # Get additional stats
        chunks = await metadata_service.get_chunks_by_document(document_id)
        
        return DocumentResponse(
            id=document.id,
            filename=document.filename,
            file_type=document.file_type,
            file_size=document.file_size,
            status=document.processing_status,
            upload_timestamp=document.upload_timestamp,
            chunk_count=len(chunks),
            entity_count=0,  # TODO: Get from graph service
            error_message=document.error_message
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting document {document_id}: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error getting document: {str(e)}"
        )

@router.get("/documents", response_model=DocumentListResponse)
async def list_documents(
    page: int = 1,
    page_size: int = 20,
    status: Optional[ProcessingStatus] = None
):
    """List documents with pagination"""
    
    try:
        if page < 1:
            raise HTTPException(status_code=400, detail="Page must be >= 1")
        
        if page_size < 1 or page_size > 100:
            raise HTTPException(status_code=400, detail="Page size must be between 1 and 100")
        
        documents = await metadata_service.list_documents(page, page_size, status)
        
        # Convert to response format
        document_responses = []
        for doc in documents:
            chunks = await metadata_service.get_chunks_by_document(str(doc.id))
            
            document_responses.append(DocumentResponse(
                id=doc.id,
                filename=doc.filename,
                file_type=doc.file_type,
                file_size=doc.file_size,
                status=doc.processing_status,
                upload_timestamp=doc.upload_timestamp,
                chunk_count=len(chunks),
                entity_count=0,  # TODO: Get from graph service
                error_message=doc.error_message
            ))
        
        return DocumentListResponse(
            documents=document_responses,
            total=len(document_responses),  # TODO: Get actual total count
            page=page,
            page_size=page_size
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error listing documents: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error listing documents: {str(e)}"
        )

@router.delete("/documents/{document_id}")
async def delete_document(document_id: str):
    """Delete a document and its associated data"""
    
    try:
        # Check if document exists
        document = await metadata_service.get_document(document_id)
        if not document:
            raise HTTPException(status_code=404, detail="Document not found")
        
        # TODO: Implement document deletion
        # - Delete file from storage
        # - Delete chunks from database
        # - Delete graph nodes/relationships
        # - Delete document metadata
        
        logger.info(f"Document deletion requested: {document_id}")
        
        return {
            "message": "Document deletion queued",
            "document_id": document_id
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting document {document_id}: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error deleting document: {str(e)}"
        )

@router.get("/documents/stats")
async def get_document_stats():
    """Get document processing statistics"""
    
    try:
        stats = await metadata_service.get_document_stats()
        return stats
        
    except Exception as e:
        logger.error(f"Error getting document stats: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error getting document stats: {str(e)}"
        )