from fastapi import APIRouter, HTTPException, Depends
from src.models.query import QueryRequest, QueryResponse, GraphExplorationRequest, GraphExplorationResponse
from src.services.query_orchestrator import query_orchestrator
from src.services.graph_service import graph_service
from src.utils.logging import get_logger

router = APIRouter()
logger = get_logger(__name__)

@router.post("/query", response_model=QueryResponse)
async def process_query(query_request: QueryRequest):
    """Process a natural language query using GraphRAG"""
    
    try:
        logger.info(f"Processing query: {query_request.query[:100]}...")
        
        # Process query through GraphRAG pipeline
        response = await query_orchestrator.process_query(query_request)
        
        logger.info(f"Query processed successfully with confidence {response.confidence}")
        return response
        
    except Exception as e:
        logger.error(f"Error processing query: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error processing query: {str(e)}"
        )

@router.post("/graph/explore", response_model=GraphExplorationResponse)
async def explore_graph(exploration_request: GraphExplorationRequest):
    """Explore the knowledge graph starting from specific entities"""
    
    try:
        logger.info(f"Exploring graph from entities: {exploration_request.start_entities}")
        
        subgraph = await graph_service.explore_subgraph(
            start_entities=exploration_request.start_entities,
            max_depth=exploration_request.max_depth,
            relationship_types=exploration_request.relationship_types
        )
        
        response = GraphExplorationResponse(
            subgraph=subgraph,
            node_count=subgraph["node_count"],
            relationship_count=subgraph["relationship_count"],
            exploration_depth=exploration_request.max_depth
        )
        
        logger.info(f"Graph exploration completed: {response.node_count} nodes, {response.relationship_count} relationships")
        return response
        
    except Exception as e:
        logger.error(f"Error exploring graph: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error exploring graph: {str(e)}"
        )

@router.get("/query/health")
async def query_service_health():
    """Check query service health"""
    
    try:
        health = await query_orchestrator.health_check()
        
        return {
            "service": "query_orchestrator",
            "status": "healthy" if health else "unhealthy",
            "components": {
                "embedding_service": "active",
                "graph_service": "active", 
                "metadata_service": "active",
                "knowledge_extraction": "active"
            }
        }
        
    except Exception as e:
        logger.error(f"Query service health check failed: {str(e)}")
        raise HTTPException(
            status_code=503,
            detail="Query service health check failed"
        )