from fastapi import APIRouter, HTTPException, Query
from typing import List, Optional
from src.services.graph_service import graph_service
from src.models.graph import GraphStats, SubgraphResponse
from src.utils.logging import get_logger

router = APIRouter()
logger = get_logger(__name__)

@router.get("/graph/stats", response_model=GraphStats)
async def get_graph_statistics():
    """Get comprehensive graph statistics"""
    
    try:
        stats = await graph_service.get_graph_stats()
        logger.info(f"Retrieved graph stats: {stats.total_nodes} nodes, {stats.total_relationships} relationships")
        return stats
        
    except Exception as e:
        logger.error(f"Error getting graph stats: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error getting graph statistics: {str(e)}"
        )

@router.get("/graph/entities/{entity_name}")
async def get_entity_details(entity_name: str):
    """Get details for a specific entity"""
    
    try:
        entities = await graph_service.find_entities_by_names([entity_name])
        
        if not entities:
            raise HTTPException(status_code=404, detail="Entity not found")
        
        entity = entities[0]
        logger.info(f"Retrieved entity details for: {entity_name}")
        return entity
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting entity {entity_name}: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error getting entity details: {str(e)}"
        )

@router.get("/graph/entities")
async def search_entities(
    query: str = Query(..., description="Search query for entities"),
    limit: int = Query(10, ge=1, le=100, description="Maximum number of results")
):
    """Search for entities by name"""
    
    try:
        # This is a simplified search - in a real implementation,
        # you'd want more sophisticated search capabilities
        
        # For now, we'll use the graph service to find entities
        # that contain the query string (this would need to be implemented)
        
        logger.info(f"Searching entities with query: {query}")
        
        # TODO: Implement entity search in graph service
        # entities = await graph_service.search_entities(query, limit)
        
        # Placeholder response
        return {
            "query": query,
            "results": [],
            "total": 0,
            "message": "Entity search not yet implemented"
        }
        
    except Exception as e:
        logger.error(f"Error searching entities: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error searching entities: {str(e)}"
        )

@router.get("/graph/relationships")
async def get_relationships(
    source_entity: Optional[str] = Query(None, description="Source entity name"),
    target_entity: Optional[str] = Query(None, description="Target entity name"),
    relationship_type: Optional[str] = Query(None, description="Relationship type"),
    limit: int = Query(50, ge=1, le=200, description="Maximum number of results")
):
    """Get relationships with optional filtering"""
    
    try:
        logger.info(f"Getting relationships with filters: source={source_entity}, target={target_entity}, type={relationship_type}")
        
        # TODO: Implement relationship filtering in graph service
        # relationships = await graph_service.get_relationships(
        #     source_entity=source_entity,
        #     target_entity=target_entity,
        #     relationship_type=relationship_type,
        #     limit=limit
        # )
        
        # Placeholder response
        return {
            "relationships": [],
            "total": 0,
            "filters": {
                "source_entity": source_entity,
                "target_entity": target_entity,
                "relationship_type": relationship_type
            },
            "message": "Relationship filtering not yet implemented"
        }
        
    except Exception as e:
        logger.error(f"Error getting relationships: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error getting relationships: {str(e)}"
        )

@router.get("/graph/path")
async def find_shortest_path(
    start_entity: str = Query(..., description="Starting entity name"),
    end_entity: str = Query(..., description="Ending entity name"),
    max_length: int = Query(5, ge=1, le=10, description="Maximum path length")
):
    """Find shortest path between two entities"""
    
    try:
        logger.info(f"Finding path from {start_entity} to {end_entity}")
        
        path = await graph_service.find_shortest_path(start_entity, end_entity, max_length)
        
        if not path:
            return {
                "start_entity": start_entity,
                "end_entity": end_entity,
                "path": None,
                "message": "No path found between entities"
            }
        
        return {
            "start_entity": start_entity,
            "end_entity": end_entity,
            "path": path,
            "path_length": path.path_length,
            "relevance_score": path.relevance_score
        }
        
    except Exception as e:
        logger.error(f"Error finding path: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error finding path: {str(e)}"
        )

@router.get("/graph/subgraph")
async def get_subgraph(
    entities: List[str] = Query(..., description="List of entity names to start from"),
    max_depth: int = Query(2, ge=1, le=5, description="Maximum exploration depth"),
    relationship_types: Optional[List[str]] = Query(None, description="Filter by relationship types"),
    limit: int = Query(100, ge=1, le=500, description="Maximum number of nodes to return")
):
    """Get a subgraph starting from specified entities"""
    
    try:
        logger.info(f"Getting subgraph for entities: {entities}")
        
        subgraph = await graph_service.explore_subgraph(
            start_entities=entities,
            max_depth=max_depth,
            relationship_types=relationship_types
        )
        
        # Limit results if needed
        if len(subgraph["nodes"]) > limit:
            subgraph["nodes"] = subgraph["nodes"][:limit]
            subgraph["node_count"] = len(subgraph["nodes"])
        
        return SubgraphResponse(
            nodes=subgraph["nodes"],
            relationships=subgraph["relationships"],
            stats=GraphStats(
                total_nodes=subgraph["node_count"],
                total_relationships=subgraph["relationship_count"],
                node_types={},
                relationship_types={},
                average_degree=0.0,
                connected_components=1
            ),
            query_info={
                "start_entities": entities,
                "max_depth": max_depth,
                "relationship_types": relationship_types,
                "limit": limit
            }
        )
        
    except Exception as e:
        logger.error(f"Error getting subgraph: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error getting subgraph: {str(e)}"
        )

@router.get("/graph/health")
async def graph_service_health():
    """Check graph service health"""
    
    try:
        health = await graph_service.health_check()
        stats = await graph_service.get_graph_stats()
        
        return {
            "service": "graph_service",
            "status": "healthy" if health else "unhealthy",
            "statistics": {
                "total_nodes": stats.total_nodes,
                "total_relationships": stats.total_relationships,
                "node_types": len(stats.node_types),
                "relationship_types": len(stats.relationship_types)
            }
        }
        
    except Exception as e:
        logger.error(f"Graph service health check failed: {str(e)}")
        raise HTTPException(
            status_code=503,
            detail="Graph service health check failed"
        )