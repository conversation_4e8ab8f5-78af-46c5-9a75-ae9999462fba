from fastapi import <PERSON>AP<PERSON>, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from contextlib import asynccontextmanager
import uvicorn

from src.utils.config import settings
from src.utils.logging import configure_logging, get_logger
from src.database.neo4j_client import neo4j_client
from src.database.postgres_client import postgres_client
from src.database.redis_client import redis_client
from src.api.routes import documents, query, graph, health
from src.api.middleware.rate_limiting import RateLimitMiddleware

# Configure logging
configure_logging()
logger = get_logger(__name__)

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager"""
    # Startup
    logger.info("Starting Project Synapse API")
    
    try:
        # Initialize database connections
        await neo4j_client.connect()
        await neo4j_client.create_constraints()
        
        await postgres_client.connect()
        await redis_client.connect()
        
        logger.info("All database connections established")
        
        yield
        
    except Exception as e:
        logger.error(f"Failed to initialize databases: {e}")
        raise
    
    finally:
        # Shutdown
        logger.info("Shutting down Project Synapse API")
        
        await neo4j_client.close()
        await postgres_client.close()
        await redis_client.close()
        
        logger.info("All database connections closed")

# Create FastAPI application
app = FastAPI(
    title="Project Synapse - Dynamic Knowledge Graph & GraphRAG System",
    description="A dynamic knowledge management platform that can ingest any document type and build a flexible, schema-less knowledge graph using OpenIE, with sophisticated GraphRAG capabilities for contextual query answering.",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan
)

# Add middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"] if settings.DEBUG else ["https://yourdomain.com"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.add_middleware(
    TrustedHostMiddleware,
    allowed_hosts=["*"] if settings.DEBUG else ["yourdomain.com", "*.yourdomain.com"]
)

app.add_middleware(RateLimitMiddleware)

# Include routers
app.include_router(health.router, prefix="/api/v1", tags=["health"])
app.include_router(documents.router, prefix="/api/v1", tags=["documents"])
app.include_router(query.router, prefix="/api/v1", tags=["query"])
app.include_router(graph.router, prefix="/api/v1", tags=["graph"])

@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "message": "Welcome to Project Synapse - Dynamic Knowledge Graph & GraphRAG System",
        "version": "1.0.0",
        "docs": "/docs",
        "health": "/api/v1/health"
    }

@app.exception_handler(HTTPException)
async def http_exception_handler(request, exc):
    """Global HTTP exception handler"""
    logger.error(f"HTTP {exc.status_code}: {exc.detail}")
    return {
        "error": {
            "status_code": exc.status_code,
            "detail": exc.detail,
            "timestamp": "2024-01-01T00:00:00Z"  # This would be dynamic in real implementation
        }
    }

@app.exception_handler(Exception)
async def general_exception_handler(request, exc):
    """Global exception handler"""
    logger.error(f"Unhandled exception: {str(exc)}")
    return {
        "error": {
            "status_code": 500,
            "detail": "Internal server error",
            "timestamp": "2024-01-01T00:00:00Z"  # This would be dynamic in real implementation
        }
    }

if __name__ == "__main__":
    uvicorn.run(
        "src.api.main:app",
        host=settings.API_HOST,
        port=settings.API_PORT,
        reload=settings.DEBUG,
        workers=1 if settings.DEBUG else settings.API_WORKERS
    )