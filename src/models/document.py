from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any
from datetime import datetime
from enum import Enum
import uuid

class ProcessingStatus(str, Enum):
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"

class DocumentUpload(BaseModel):
    filename: str
    content_type: str
    size: int

class DocumentMetadata(BaseModel):
    id: uuid.UUID = Field(default_factory=uuid.uuid4)
    filename: str
    file_type: str
    file_size: int
    upload_timestamp: datetime = Field(default_factory=datetime.utcnow)
    processing_status: ProcessingStatus = ProcessingStatus.PENDING
    metadata: Optional[Dict[str, Any]] = None
    error_message: Optional[str] = None
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)

class TextChunk(BaseModel):
    id: uuid.UUID = Field(default_factory=uuid.uuid4)
    document_id: uuid.UUID
    chunk_text: str
    chunk_index: int
    token_count: int
    embedding_vector: Optional[List[float]] = None
    metadata: Optional[Dict[str, Any]] = None
    created_at: datetime = Field(default_factory=datetime.utcnow)

class DocumentResponse(BaseModel):
    id: uuid.UUID
    filename: str
    file_type: str
    file_size: int
    status: ProcessingStatus
    upload_timestamp: datetime
    chunk_count: Optional[int] = None
    entity_count: Optional[int] = None
    error_message: Optional[str] = None

class DocumentListResponse(BaseModel):
    documents: List[DocumentResponse]
    total: int
    page: int
    page_size: int

class DocumentUploadResponse(BaseModel):
    document_id: uuid.UUID
    filename: str
    status: ProcessingStatus
    message: str

class DocumentProcessingUpdate(BaseModel):
    document_id: uuid.UUID
    status: ProcessingStatus
    progress: Optional[float] = None
    current_step: Optional[str] = None
    error_message: Optional[str] = None
    chunks_processed: Optional[int] = None
    entities_extracted: Optional[int] = None