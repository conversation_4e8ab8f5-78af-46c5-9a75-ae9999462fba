from pydantic import BaseModel, Field
from typing import List, Dict, Optional, Any
from enum import Enum
import uuid

class EntityType(str, Enum):
    PERSON = "person"
    ORGANIZATION = "organization"
    LOCATION = "location"
    CONCEPT = "concept"
    EVENT = "event"
    OTHER = "other"

class Entity(BaseModel):
    name: str
    type: EntityType = EntityType.OTHER
    properties: Dict[str, Any] = {}
    confidence: float = 1.0

class Relationship(BaseModel):
    subject: str
    predicate: str
    object: str
    confidence: float = 1.0
    source_chunk_id: Optional[str] = None

class Triple(BaseModel):
    subject: Entity
    relationship: str
    object: Entity
    confidence: float = 1.0
    context: Optional[str] = None

class GraphNode(BaseModel):
    id: str
    name: str
    type: EntityType
    properties: Dict[str, Any] = {}
    relationships: List[Dict[str, Any]] = []

class GraphRelationship(BaseModel):
    id: str
    type: str
    source_node: str
    target_node: str
    properties: Dict[str, Any] = {}
    confidence: float = 1.0

class GraphPath(BaseModel):
    nodes: List[GraphNode]
    relationships: List[GraphRelationship]
    path_length: int
    relevance_score: float

class GraphChunkLink(BaseModel):
    id: uuid.UUID = Field(default_factory=uuid.uuid4)
    node_id: str
    chunk_id: uuid.UUID
    relationship_type: Optional[str] = None
    confidence_score: float = 1.0

class GraphStats(BaseModel):
    total_nodes: int
    total_relationships: int
    node_types: Dict[str, int]
    relationship_types: Dict[str, int]
    average_degree: float
    connected_components: int

class SubgraphResponse(BaseModel):
    nodes: List[GraphNode]
    relationships: List[GraphRelationship]
    stats: GraphStats
    query_info: Dict[str, Any]