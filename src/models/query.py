from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
from datetime import datetime
import uuid

class QueryRequest(BaseModel):
    query: str
    context_limit: int = 10
    max_graph_depth: int = 3
    include_sources: bool = True
    user_id: Optional[str] = None

class SourceCitation(BaseModel):
    document_id: str
    document_name: str
    chunk_id: str
    chunk_text: str
    relevance_score: float

class QueryResponse(BaseModel):
    answer: str
    confidence: float
    sources: List[SourceCitation]
    processing_time: float
    graph_nodes_explored: int
    timestamp: datetime = Field(default_factory=datetime.utcnow)

class GraphExplorationRequest(BaseModel):
    start_entities: List[str]
    max_depth: int = 2
    relationship_types: Optional[List[str]] = None
    limit: int = 50

class GraphExplorationResponse(BaseModel):
    subgraph: Dict[str, Any]
    node_count: int
    relationship_count: int
    exploration_depth: int

class QueryLog(BaseModel):
    id: uuid.UUID = Field(default_factory=uuid.uuid4)
    user_id: Optional[uuid.UUID] = None
    query_text: str
    response_text: str
    processing_time: float
    confidence_score: float
    sources_count: int = 0
    graph_nodes_explored: int = 0
    created_at: datetime = Field(default_factory=datetime.utcnow)

class QueryAnalytics(BaseModel):
    total_queries: int
    average_processing_time: float
    average_confidence: float
    most_common_entities: List[Dict[str, Any]]
    query_success_rate: float
    time_period: str

class SimilarQuery(BaseModel):
    query_text: str
    similarity_score: float
    cached_response: Optional[QueryResponse] = None