# Project Synapse - Control Flow Documentation

This document provides a detailed step-by-step control flow for the Dynamic Knowledge Graph & GraphRAG System, showing exactly how processing flows through the system from entry points to final outputs.

## System Entry Points

The system has two main entry points:
1. **Document Ingestion**: `/api/v1/documents/upload`
2. **Query Processing**: `/api/v1/query`

---

## 🔄 Document Ingestion Flow

### Entry Point: `POST /api/v1/documents/upload`
**File**: [`src/api/routes/documents.py`](src/api/routes/documents.py:15)

### Step-by-Step Processing:

#### 1. **API Request Reception**
```
Client → FastAPI → documents.upload_document()
```
- **Location**: `src/api/routes/documents.py:15`
- **Input**: Multipart file upload + optional metadata
- **Validation**: File type, size, filename checks

#### 2. **File Validation & Storage**
```
upload_document() → File validation → Local storage
```
- **Process**: 
  - Check file extension against `SUPPORTED_FORMATS`
  - Validate file size against `MAX_FILE_SIZE`
  - Generate unique `document_id` (UUID)
  - Save file to `uploads/{document_id}_{filename}`

#### 3. **Document Metadata Creation**
```
File storage → DocumentMetadata creation → PostgreSQL
```
- **Location**: `src/services/metadata_service.py:15`
- **Process**:
  - Create `DocumentMetadata` object with `PENDING` status
  - Call `metadata_service.create_document()`
  - Insert record into PostgreSQL `documents` table

#### 4. **Queue for Processing** ⚠️ *[TODO: Not yet implemented]*
```
Metadata creation → Celery task queue → Background processing
```
- **Expected Flow**:
  - Queue document for async processing
  - Return immediate response to client
  - Background worker picks up task

#### 5. **Response to Client**
```
Processing queued → DocumentUploadResponse → Client
```
- **Output**: Document ID, filename, status, success message

---

## 🔄 Background Document Processing Flow ⚠️ *[TODO: Implementation needed]*

### Entry Point: Celery Worker Task

#### 1. **Text Extraction & Preprocessing**
```
Celery worker → Text extraction → Chunking → Chunk storage
```
- **Expected Location**: `src/services/preprocessing.py` (not yet created)
- **Process**:
  - Extract text from uploaded file using `unstructured.io`
  - Clean and normalize text
  - Split into semantic chunks (1000 chars, 150 overlap)
  - Store chunks in PostgreSQL via `metadata_service.create_chunk()`

#### 2. **Knowledge Extraction**
```
Text chunks → OpenAI GPT-4 → Entity-Relationship triples
```
- **Location**: `src/services/knowledge_extraction.py:25`
- **Process**:
  - For each chunk, call `knowledge_extraction_service.extract_triples()`
  - Send chunk to OpenAI GPT-4 with OpenIE prompt
  - Parse JSON response into `Triple` objects
  - Extract entities and relationships

#### 3. **Embedding Generation**
```
Text chunks → Mock embedding service → Vector storage
```
- **Location**: `src/services/embedding_service.py:25`
- **Process**:
  - Call `embedding_service.generate_embedding()` for each chunk
  - Generate consistent mock vectors using text hash
  - Store embeddings (currently in-memory cache)

#### 4. **Graph Storage**
```
Triples → Neo4j graph database → Entity/Relationship nodes
```
- **Location**: `src/services/graph_service.py:15`
- **Process**:
  - Call `graph_service.store_triples()` for each chunk's triples
  - Create/merge entity nodes in Neo4j
  - Create relationship edges between entities
  - Link chunks to entities via `MENTIONS` relationships

#### 5. **Status Update**
```
Processing complete → Update document status → COMPLETED
```
- **Process**:
  - Update document status in PostgreSQL
  - Log processing completion
  - Document ready for querying

---

## 🔍 Query Processing Flow

### Entry Point: `POST /api/v1/query`
**File**: [`src/api/routes/query.py`](src/api/routes/query.py:10)

### Step-by-Step Processing:

#### 1. **Query Request Reception**
```
Client → FastAPI → query.process_query() → GraphRAGOrchestrator
```
- **Location**: `src/api/routes/query.py:10`
- **Input**: `QueryRequest` with query text, limits, depth settings
- **Routing**: → `src/services/query_orchestrator.py:25`

#### 2. **GraphRAG Pipeline Orchestration**
```
QueryRequest → query_orchestrator.process_query() → Multi-step pipeline
```
- **Location**: `src/services/query_orchestrator.py:25`
- **Process**: Coordinates 5-step GraphRAG pipeline

### GraphRAG Pipeline Steps:

#### Step 2.1: **Initial Semantic Retrieval**
```
Query text → Mock embedding → Similarity search → Relevant chunks
```
- **Location**: `src/services/query_orchestrator.py:65`
- **Process**:
  - Generate query embedding via `embedding_service.generate_embedding()`
  - Get all chunk embeddings via `metadata_service.get_all_chunks_with_embeddings()`
  - Find similar chunks via `embedding_service.find_similar_chunks()`
  - Return top-k most relevant chunks

#### Step 2.2: **Entity Identification**
```
Relevant chunks → OpenAI GPT-4 → Key entities
```
- **Location**: `src/services/query_orchestrator.py:105`
- **Process**:
  - Combine top chunk texts
  - Send to `knowledge_extraction_service.extract_entities_only()`
  - OpenAI GPT-4 identifies key entities from text
  - Return list of entity names

#### Step 2.3: **Graph Exploration**
```
Key entities → Neo4j traversal → Related subgraph
```
- **Location**: `src/services/query_orchestrator.py:135`
- **Process**:
  - Call `graph_service.explore_subgraph()` with identified entities
  - Neo4j traverses relationships up to max_depth
  - Return subgraph with nodes and relationships

#### Step 2.4: **Context Augmentation**
```
Initial chunks + Graph entities → Additional chunks → Comprehensive context
```
- **Location**: `src/services/query_orchestrator.py:155`
- **Process**:
  - Get entity names from subgraph
  - Find additional chunks mentioning these entities
  - Combine initial chunks + graph chunks
  - Create comprehensive context object

#### Step 2.5: **Answer Synthesis**
```
Query + Augmented context → OpenAI GPT-4 → Final answer
```
- **Location**: `src/services/query_orchestrator.py:185`
- **Process**:
  - Format context for LLM consumption
  - Send query + context to OpenAI GPT-4
  - Parse JSON response with answer, confidence, reasoning
  - Return structured answer

#### 3. **Response Preparation**
```
Answer + Context → Citations → QueryResponse
```
- **Location**: `src/services/query_orchestrator.py:280`
- **Process**:
  - Prepare source citations from chunks
  - Calculate processing time and metrics
  - Create `QueryResponse` object

#### 4. **Response to Client**
```
QueryResponse → FastAPI → JSON response → Client
```
- **Output**: Answer, confidence, sources, processing time, metrics

---

## 🔄 Graph Exploration Flow

### Entry Point: `POST /api/v1/graph/explore`
**File**: [`src/api/routes/query.py`](src/api/routes/query.py:35)

#### 1. **Exploration Request**
```
Client → FastAPI → graph.explore_graph()
```
- **Input**: Start entities, max depth, relationship filters

#### 2. **Neo4j Graph Traversal**
```
Exploration request → graph_service.explore_subgraph() → Neo4j query
```
- **Location**: `src/services/graph_service.py:85`
- **Process**:
  - Build Cypher query with path traversal
  - Execute multi-hop relationship discovery
  - Return nodes and relationships

#### 3. **Response Formatting**
```
Subgraph data → GraphExplorationResponse → Client
```
- **Output**: Subgraph structure, node/relationship counts, exploration metadata

---

## 🏥 Health Check Flow

### Entry Point: `GET /api/v1/health`
**File**: [`src/api/routes/health.py`](src/api/routes/health.py:12)

#### 1. **Service Health Verification**
```
Health request → Test all services → Aggregate status
```
- **Process**:
  - Test Neo4j connection
  - Test PostgreSQL connection  
  - Test Redis connection
  - Test application services
  - Determine overall health status

#### 2. **Detailed Statistics** (Optional)
```
Basic health → Service statistics → Detailed response
```
- **Location**: `src/api/routes/health.py:75`
- **Process**:
  - Get graph statistics
  - Get document statistics
  - Get embedding service stats
  - Return comprehensive health report

---

## 🔧 System Initialization Flow

### Entry Point: Application Startup
**File**: [`src/api/main.py`](src/api/main.py:15)

#### 1. **Application Lifecycle**
```
FastAPI startup → Database connections → Service initialization
```
- **Process**:
  - Configure structured logging
  - Connect to Neo4j and create constraints/indexes
  - Connect to PostgreSQL pool
  - Connect to Redis
  - Initialize all services

#### 2. **Middleware Setup**
```
Service init → CORS → Rate limiting → Request processing
```
- **Components**:
  - CORS middleware for cross-origin requests
  - Rate limiting middleware (100 requests/minute)
  - Trusted host middleware
  - Exception handlers

---

## 📊 Data Flow Summary

### Document Processing Data Flow:
```
File Upload → PostgreSQL (metadata) → Text Extraction → OpenAI (triples) → Neo4j (graph) → Mock Embeddings → Ready for Query
```

### Query Processing Data Flow:
```
Query → Mock Embeddings (similarity) → PostgreSQL (chunks) → OpenAI (entities) → Neo4j (graph traversal) → Context Augmentation → OpenAI (synthesis) → Response
```

### Key Data Stores:
- **PostgreSQL**: Document metadata, text chunks, processing status
- **Neo4j**: Knowledge graph (entities, relationships, chunk links)
- **Redis**: Caching, rate limiting, message brokering
- **File System**: Original uploaded documents
- **Memory**: Mock embedding cache

---

## 🚀 Quick Start Processing Flow

1. **Start System**: `docker-compose up --build -d`
2. **Upload Document**: `POST /api/v1/documents/upload` → Document queued
3. **Background Processing**: Document → Chunks → Triples → Graph → Ready
4. **Query System**: `POST /api/v1/query` → GraphRAG pipeline → Answer
5. **Explore Graph**: `POST /api/v1/graph/explore` → Subgraph visualization

This control flow shows exactly how data moves through the system from entry points to final outputs, making it clear where each processing step occurs and how components interact.