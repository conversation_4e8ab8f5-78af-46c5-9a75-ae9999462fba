# Project Synapse - Dynamic Knowledge Graph & GraphRAG System

A dynamic knowledge management platform that can ingest any document type and build a flexible, schema-less knowledge graph using OpenIE, with sophisticated GraphRAG capabilities for contextual query answering.

## Features

- **Universal Document Support**: PDF, DOCX, TXT, MD, HTML
- **OpenIE Knowledge Extraction**: Automated entity and relationship discovery using OpenAI GPT-4
- **Dynamic Knowledge Graph**: Schema-less graph storage in Neo4j
- **GraphRAG Query Processing**: Multi-step retrieval combining semantic search and graph traversal
- **Mock Embedding Service**: Placeholder for future vector database integration
- **Scalable Architecture**: Microservices with async processing and containerization

## Architecture

The system consists of two main pipelines:

1. **Asynchronous Ingestion Pipeline**: Processes documents and builds knowledge assets
2. **Synchronous Retrieval Pipeline**: Handles real-time queries using GraphRAG

### Technology Stack

- **Backend**: Python + FastAPI + Celery + Redis
- **Graph Database**: Neo4j
- **Metadata Database**: PostgreSQL
- **LLM Service**: OpenAI GPT-4 API
- **Containerization**: Docker + Docker Compose

## Quick Start

### Prerequisites

- Docker and Docker Compose
- OpenAI API key

### Setup

1. Clone the repository:
```bash
git clone <repository-url>
cd project-synapse
```

2. Copy environment template:
```bash
cp .env.example .env
```

3. Edit `.env` with your configuration:
```bash
# Required: Add your OpenAI API key
OPENAI_API_KEY=your_openai_api_key_here

# Required: Set secure passwords
NEO4J_PASSWORD=your_secure_neo4j_password
POSTGRES_PASSWORD=your_secure_postgres_password
SECRET_KEY=your_very_secure_secret_key_here_minimum_32_characters
```

4. Build and start services:
```bash
docker-compose up --build -d
```

5. Check service health:
```bash
curl http://localhost:8000/api/v1/health
```

### API Documentation

Once running, visit:
- **API Documentation**: http://localhost:8000/docs
- **Alternative Docs**: http://localhost:8000/redoc
- **Neo4j Browser**: http://localhost:7474

## API Endpoints

### Document Management
- `POST /api/v1/documents/upload` - Upload documents
- `GET /api/v1/documents` - List documents
- `GET /api/v1/documents/{id}` - Get document details
- `DELETE /api/v1/documents/{id}` - Delete document

### Query Processing
- `POST /api/v1/query` - Process natural language queries
- `POST /api/v1/graph/explore` - Explore knowledge graph

### Graph Operations
- `GET /api/v1/graph/stats` - Get graph statistics
- `GET /api/v1/graph/entities/{name}` - Get entity details
- `GET /api/v1/graph/subgraph` - Get subgraph data

### Health Monitoring
- `GET /api/v1/health` - Service health check
- `GET /api/v1/health/detailed` - Detailed health with stats

## Usage Examples

### Upload a Document
```bash
curl -X POST "http://localhost:8000/api/v1/documents/upload" \
  -H "Content-Type: multipart/form-data" \
  -F "file=@document.pdf"
```

### Query the Knowledge Base
```bash
curl -X POST "http://localhost:8000/api/v1/query" \
  -H "Content-Type: application/json" \
  -d '{
    "query": "What is the relationship between Krishna and Arjuna?",
    "context_limit": 10,
    "max_graph_depth": 2
  }'
```

### Explore the Graph
```bash
curl -X POST "http://localhost:8000/api/v1/graph/explore" \
  -H "Content-Type: application/json" \
  -d '{
    "start_entities": ["Krishna", "Arjuna"],
    "max_depth": 2
  }'
```

## Development

### Local Development Setup

1. Install Python dependencies:
```bash
pip install -r requirements/development.txt
```

2. Start databases with Docker:
```bash
docker-compose up postgres neo4j redis -d
```

3. Run the API server:
```bash
python -m src.api.main
```

### Running Tests

```bash
pytest src/tests/
```

### Code Quality

```bash
# Format code
black src/
isort src/

# Lint code
flake8 src/

# Type checking
mypy src/
```

## Architecture Documentation

For detailed architecture information, see:
- [`architecture.md`](architecture.md) - System architecture overview
- [`technical_specification.md`](technical_specification.md) - Implementation details
- [`implementation_guide.md`](implementation_guide.md) - GraphRAG orchestrator guide
- [`deployment_guide.md`](deployment_guide.md) - Production deployment
- [`project_summary.md`](project_summary.md) - Executive summary

## Key Components

### GraphRAG Query Pipeline

1. **Initial Retrieval**: Semantic search using mock embeddings
2. **Entity Identification**: Extract entities from relevant chunks
3. **Graph Exploration**: Find related entities and relationships
4. **Context Augmentation**: Gather comprehensive context
5. **Answer Synthesis**: Generate final answer using OpenAI GPT-4

### Mock Embedding Service

The system includes a mock embedding service that generates consistent dummy vectors for development and testing. This allows the focus to remain on graph-based retrieval while maintaining the interface for future vector database integration.

### Knowledge Extraction

Uses OpenAI GPT-4 to extract structured knowledge from text in the form of subject-predicate-object triples, building a dynamic knowledge graph without predefined schemas.

## Monitoring

### Health Checks

- **Liveness**: `GET /api/v1/health/live`
- **Readiness**: `GET /api/v1/health/ready`
- **Detailed**: `GET /api/v1/health/detailed`

### Metrics

The system tracks:
- Document processing throughput
- Query response times
- Graph growth metrics
- API endpoint performance

## Security

- JWT-based authentication (placeholder)
- Rate limiting middleware
- Input validation and sanitization
- CORS configuration
- TLS/SSL support (production)

## Scaling

### Horizontal Scaling
- Multiple API instances behind load balancer
- Auto-scaling Celery workers
- Neo4j clustering support
- PostgreSQL read replicas

### Performance Optimization
- Redis caching for frequent operations
- Database connection pooling
- Batch processing for large operations
- Query result pagination

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For questions and support:
- Check the documentation in the `docs/` directory
- Review the API documentation at `/docs`
- Open an issue on GitHub

## Roadmap

### Phase 2 Features
- Real-time graph updates
- Advanced entity disambiguation
- Graph visualization interface
- User feedback integration

### Future Enhancements
- Vector database integration (Weaviate/Pinecone)
- Multi-language support
- Advanced analytics dashboard
- Custom model training capabilities