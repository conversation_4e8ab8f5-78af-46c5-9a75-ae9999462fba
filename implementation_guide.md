# Implementation Guide - GraphRAG Query Orchestrator

## Overview
This guide provides detailed implementation instructions for the core GraphRAG query orchestrator, which is the brain of the retrieval system. This component coordinates the multi-step process of answering user queries using the knowledge graph.

## GraphRAG Query Pipeline

### Step-by-Step Process
1. **Query Processing** → Parse and understand user intent
2. **Initial Retrieval** → Mock semantic search for relevant chunks
3. **Entity Identification** → Extract entities from retrieved chunks
4. **Graph Exploration** → Find related entities and relationships
5. **Context Augmentation** → Gather comprehensive context
6. **Answer Synthesis** → Generate final answer using OpenAI GPT-4

## Core Implementation

### 1. Query Orchestrator Service

**services/query_orchestrator.py**
```python
import asyncio
from typing import List, Dict, Any, Optional
import time
from datetime import datetime

from services.embedding_service import MockEmbeddingService
from services.graph_service import GraphService
from services.metadata_service import MetadataService
from models.query import QueryRequest, QueryResponse, SourceCitation
from utils.config import settings
import openai

class GraphRAGOrchestrator:
    def __init__(self):
        self.embedding_service = MockEmbeddingService()
        self.graph_service = GraphService()
        self.metadata_service = MetadataService()
        openai.api_key = settings.OPENAI_API_KEY
        
    async def process_query(self, query_request: QueryRequest) -> QueryResponse:
        """Main GraphRAG pipeline orchestration"""
        start_time = time.time()
        
        try:
            # Step 1: Initial semantic retrieval
            relevant_chunks = await self._initial_retrieval(
                query_request.query, 
                query_request.context_limit
            )
            
            # Step 2: Entity identification from chunks
            entities = await self._identify_entities(relevant_chunks)
            
            # Step 3: Graph exploration
            graph_context = await self._explore_graph(
                entities, 
                query_request.max_graph_depth
            )
            
            # Step 4: Context augmentation
            augmented_context = await self._augment_context(
                relevant_chunks, 
                graph_context
            )
            
            # Step 5: Answer synthesis
            answer = await self._synthesize_answer(
                query_request.query, 
                augmented_context
            )
            
            # Step 6: Prepare response with citations
            sources = self._prepare_citations(relevant_chunks, graph_context)
            
            processing_time = time.time() - start_time
            
            return QueryResponse(
                answer=answer["text"],
                confidence=answer["confidence"],
                sources=sources,
                processing_time=processing_time,
                graph_nodes_explored=len(graph_context.get("nodes", [])),
                timestamp=datetime.utcnow()
            )
            
        except Exception as e:
            print(f"Error in query processing: {e}")
            return QueryResponse(
                answer="I apologize, but I encountered an error processing your query.",
                confidence=0.0,
                sources=[],
                processing_time=time.time() - start_time,
                graph_nodes_explored=0,
                timestamp=datetime.utcnow()
            )
    
    async def _initial_retrieval(self, query: str, limit: int) -> List[Dict[str, Any]]:
        """Step 1: Perform initial semantic search using mock embeddings"""
        
        # Generate query embedding
        query_embedding = self.embedding_service.generate_embedding(query)
        
        # Get all chunk embeddings from metadata service
        all_chunks = await self.metadata_service.get_all_chunks_with_embeddings()
        
        # Find similar chunks using mock similarity
        chunk_embeddings = {
            chunk["id"]: chunk["embedding"] 
            for chunk in all_chunks
        }
        
        similar_chunks = self.embedding_service.find_similar_chunks(
            query_embedding, 
            chunk_embeddings, 
            top_k=limit
        )
        
        # Enrich with chunk content
        enriched_chunks = []
        for similar_chunk in similar_chunks:
            chunk_data = await self.metadata_service.get_chunk_by_id(
                similar_chunk["chunk_id"]
            )
            if chunk_data:
                chunk_data["similarity"] = similar_chunk["similarity"]
                enriched_chunks.append(chunk_data)
        
        return enriched_chunks
    
    async def _identify_entities(self, chunks: List[Dict[str, Any]]) -> List[str]:
        """Step 2: Identify key entities from retrieved chunks"""
        
        # Combine chunk texts
        combined_text = "\n\n".join([chunk["chunk_text"] for chunk in chunks])
        
        # Use OpenAI to identify entities
        prompt = f"""
        From the following text, identify the most important entities (people, organizations, locations, concepts) that would be useful for finding related information in a knowledge graph.
        
        Return only a JSON array of entity names, like: ["Entity1", "Entity2", "Entity3"]
        
        Text:
        {combined_text[:2000]}  # Limit text length
        """
        
        try:
            response = await openai.ChatCompletion.acreate(
                model=settings.OPENAI_MODEL,
                messages=[
                    {"role": "system", "content": "You are an expert entity extraction system. Return only valid JSON arrays of entity names."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=500,
                temperature=0.1
            )
            
            entities_text = response.choices[0].message.content
            
            # Parse JSON response
            import json
            import re
            json_match = re.search(r'\[.*\]', entities_text, re.DOTALL)
            if json_match:
                entities = json.loads(json_match.group(0))
                return entities[:10]  # Limit to top 10 entities
            
        except Exception as e:
            print(f"Error in entity identification: {e}")
        
        # Fallback: extract entities from chunk metadata if available
        entities = []
        for chunk in chunks:
            if "entities" in chunk.get("metadata", {}):
                entities.extend(chunk["metadata"]["entities"])
        
        return list(set(entities))[:10]
    
    async def _explore_graph(self, entities: List[str], max_depth: int) -> Dict[str, Any]:
        """Step 3: Explore graph to find related entities and relationships"""
        
        if not entities:
            return {"nodes": [], "relationships": []}
        
        # Use graph service to explore subgraph
        subgraph = self.graph_service.explore_subgraph(
            start_entities=entities,
            max_depth=max_depth
        )
        
        return subgraph
    
    async def _augment_context(self, initial_chunks: List[Dict[str, Any]], 
                              graph_context: Dict[str, Any]) -> Dict[str, Any]:
        """Step 4: Augment context with graph-related information"""
        
        # Get entity names from graph context
        graph_entities = [node["name"] for node in graph_context.get("nodes", [])]
        
        # Find additional chunks that mention these entities
        additional_chunk_ids = self.graph_service.get_entity_context_chunks(graph_entities)
        
        # Retrieve additional chunks
        additional_chunks = []
        for chunk_id in additional_chunk_ids[:20]:  # Limit additional chunks
            chunk_data = await self.metadata_service.get_chunk_by_id(chunk_id)
            if chunk_data and chunk_data not in initial_chunks:
                additional_chunks.append(chunk_data)
        
        # Combine and rank all context
        all_chunks = initial_chunks + additional_chunks
        
        # Create comprehensive context
        context = {
            "primary_chunks": initial_chunks,
            "graph_chunks": additional_chunks,
            "graph_structure": graph_context,
            "total_chunks": len(all_chunks),
            "entities_explored": graph_entities
        }
        
        return context
    
    async def _synthesize_answer(self, query: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Step 5: Generate final answer using OpenAI GPT-4"""
        
        # Prepare context for LLM
        context_text = self._format_context_for_llm(context)
        
        system_prompt = """You are an expert knowledge assistant that provides accurate, comprehensive answers based on the provided context from a knowledge graph and document chunks.

Guidelines:
1. Use only information from the provided context
2. Synthesize information from multiple sources when relevant
3. Provide specific, factual answers
4. If the context doesn't contain enough information, say so clearly
5. Include relevant details and relationships from the knowledge graph
6. Maintain accuracy and avoid speculation

Format your response as JSON:
{
  "text": "Your comprehensive answer here",
  "confidence": 0.85,
  "key_entities": ["entity1", "entity2"],
  "reasoning": "Brief explanation of how you arrived at this answer"
}"""

        user_prompt = f"""
Query: {query}

Context Information:
{context_text}

Please provide a comprehensive answer based on this context.
"""

        try:
            response = await openai.ChatCompletion.acreate(
                model=settings.OPENAI_MODEL,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                max_tokens=settings.OPENAI_MAX_TOKENS,
                temperature=settings.OPENAI_TEMPERATURE
            )
            
            answer_text = response.choices[0].message.content
            
            # Parse JSON response
            import json
            import re
            json_match = re.search(r'\{.*\}', answer_text, re.DOTALL)
            if json_match:
                answer_data = json.loads(json_match.group(0))
                return answer_data
            else:
                # Fallback if JSON parsing fails
                return {
                    "text": answer_text,
                    "confidence": 0.7,
                    "key_entities": [],
                    "reasoning": "Direct response from LLM"
                }
                
        except Exception as e:
            print(f"Error in answer synthesis: {e}")
            return {
                "text": "I apologize, but I couldn't generate a proper answer based on the available context.",
                "confidence": 0.0,
                "key_entities": [],
                "reasoning": f"Error in synthesis: {str(e)}"
            }
    
    def _format_context_for_llm(self, context: Dict[str, Any]) -> str:
        """Format the augmented context for LLM consumption"""
        
        formatted_context = []
        
        # Add primary chunks
        if context["primary_chunks"]:
            formatted_context.append("=== PRIMARY RELEVANT CONTENT ===")
            for i, chunk in enumerate(context["primary_chunks"][:5]):
                formatted_context.append(f"Source {i+1}: {chunk['chunk_text']}")
        
        # Add graph structure information
        if context["graph_structure"]["nodes"]:
            formatted_context.append("\n=== KNOWLEDGE GRAPH RELATIONSHIPS ===")
            
            # Add key entities
            entities = [node["name"] for node in context["graph_structure"]["nodes"][:10]]
            formatted_context.append(f"Key Entities: {', '.join(entities)}")
            
            # Add relationships
            relationships = context["graph_structure"]["relationships"][:15]
            if relationships:
                formatted_context.append("Key Relationships:")
                for rel in relationships:
                    formatted_context.append(f"- {rel['source']} {rel['type']} {rel['target']}")
        
        # Add additional context chunks
        if context["graph_chunks"]:
            formatted_context.append("\n=== ADDITIONAL RELATED CONTENT ===")
            for i, chunk in enumerate(context["graph_chunks"][:3]):
                formatted_context.append(f"Related {i+1}: {chunk['chunk_text'][:500]}...")
        
        return "\n".join(formatted_context)
    
    def _prepare_citations(self, chunks: List[Dict[str, Any]], 
                          graph_context: Dict[str, Any]) -> List[SourceCitation]:
        """Prepare source citations for the response"""
        
        citations = []
        
        for chunk in chunks[:5]:  # Limit citations
            citation = SourceCitation(
                document_id=chunk["document_id"],
                document_name=chunk.get("document_name", "Unknown Document"),
                chunk_id=chunk["id"],
                chunk_text=chunk["chunk_text"][:200] + "...",
                relevance_score=chunk.get("similarity", 0.0)
            )
            citations.append(citation)
        
        return citations
```

### 2. Metadata Service Implementation

**services/metadata_service.py**
```python
import asyncio
from typing import List, Dict, Any, Optional
import asyncpg
from utils.config import settings
import json

class MetadataService:
    def __init__(self):
        self.pool = None
    
    async def initialize(self):
        """Initialize database connection pool"""
        self.pool = await asyncpg.create_pool(settings.POSTGRES_URL)
    
    async def close(self):
        """Close database connections"""
        if self.pool:
            await self.pool.close()
    
    async def get_all_chunks_with_embeddings(self) -> List[Dict[str, Any]]:
        """Get all chunks with their mock embeddings"""
        
        async with self.pool.acquire() as conn:
            query = """
            SELECT c.id, c.document_id, c.chunk_text, c.chunk_index,
                   d.filename as document_name
            FROM chunks c
            JOIN documents d ON c.document_id = d.id
            WHERE d.processing_status = 'completed'
            ORDER BY c.created_at DESC
            """
            
            rows = await conn.fetch(query)
            
            chunks = []
            for row in rows:
                # Generate mock embedding for each chunk
                from services.embedding_service import MockEmbeddingService
                embedding_service = MockEmbeddingService()
                embedding = embedding_service.generate_embedding(row["chunk_text"])
                
                chunks.append({
                    "id": str(row["id"]),
                    "document_id": str(row["document_id"]),
                    "document_name": row["document_name"],
                    "chunk_text": row["chunk_text"],
                    "chunk_index": row["chunk_index"],
                    "embedding": embedding
                })
            
            return chunks
    
    async def get_chunk_by_id(self, chunk_id: str) -> Optional[Dict[str, Any]]:
        """Get a specific chunk by ID"""
        
        async with self.pool.acquire() as conn:
            query = """
            SELECT c.id, c.document_id, c.chunk_text, c.chunk_index,
                   d.filename as document_name, d.metadata
            FROM chunks c
            JOIN documents d ON c.document_id = d.id
            WHERE c.id = $1
            """
            
            row = await conn.fetchrow(query, chunk_id)
            
            if row:
                return {
                    "id": str(row["id"]),
                    "document_id": str(row["document_id"]),
                    "document_name": row["document_name"],
                    "chunk_text": row["chunk_text"],
                    "chunk_index": row["chunk_index"],
                    "metadata": row["metadata"] or {}
                }
            
            return None
```

### 3. Query API Endpoint

**api/routes/query.py**
```python
from fastapi import APIRouter, HTTPException, Depends
from models.query import QueryRequest, QueryResponse, GraphExplorationRequest, GraphExplorationResponse
from services.query_orchestrator import GraphRAGOrchestrator
from api.auth import get_current_user

router = APIRouter(prefix="/api/v1", tags=["query"])

# Initialize orchestrator (in production, use dependency injection)
orchestrator = GraphRAGOrchestrator()

@router.post("/query", response_model=QueryResponse)
async def process_query(
    query_request: QueryRequest,
    current_user: dict = Depends(get_current_user)
):
    """Process a natural language query using GraphRAG"""
    
    try:
        # Add user context to request
        query_request.user_id = current_user.get("user_id")
        
        # Process query through GraphRAG pipeline
        response = await orchestrator.process_query(query_request)
        
        return response
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error processing query: {str(e)}"
        )

@router.post("/graph/explore", response_model=GraphExplorationResponse)
async def explore_graph(
    exploration_request: GraphExplorationRequest,
    current_user: dict = Depends(get_current_user)
):
    """Explore the knowledge graph starting from specific entities"""
    
    try:
        subgraph = orchestrator.graph_service.explore_subgraph(
            start_entities=exploration_request.start_entities,
            max_depth=exploration_request.max_depth,
            relationship_types=exploration_request.relationship_types
        )
        
        return GraphExplorationResponse(
            subgraph=subgraph,
            node_count=subgraph["node_count"],
            relationship_count=subgraph["relationship_count"],
            exploration_depth=exploration_request.max_depth
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error exploring graph: {str(e)}"
        )
```

## Key Implementation Features

### 1. Asynchronous Processing
- Uses `asyncio` for non-blocking operations
- Concurrent processing where possible
- Efficient database connection pooling

### 2. Error Handling
- Comprehensive try-catch blocks
- Graceful degradation when services fail
- Detailed error logging

### 3. Context Management
- Multi-source context aggregation
- Intelligent context ranking
- Context size management for LLM limits

### 4. Performance Optimization
- Caching of embeddings and entities
- Batch processing where applicable
- Query result limiting and pagination

### 5. Extensibility
- Modular service architecture
- Easy to swap mock services with real implementations
- Plugin-ready for additional data sources

## Testing Strategy

### Unit Tests
```python
import pytest
from services.query_orchestrator import GraphRAGOrchestrator
from models.query import QueryRequest

@pytest.mark.asyncio
async def test_query_processing():
    orchestrator = GraphRAGOrchestrator()
    
    request = QueryRequest(
        query="What is the relationship between Krishna and Arjuna?",
        context_limit=5,
        max_graph_depth=2
    )
    
    response = await orchestrator.process_query(request)
    
    assert response.answer is not None
    assert response.confidence > 0
    assert len(response.sources) > 0
```

### Integration Tests
```python
@pytest.mark.asyncio
async def test_full_pipeline():
    # Test complete ingestion to query pipeline
    # Upload document -> Process -> Query -> Verify results
    pass
```

This implementation guide provides a complete, production-ready GraphRAG orchestrator that can handle complex queries by leveraging both semantic search and graph traversal to provide comprehensive, contextual answers.