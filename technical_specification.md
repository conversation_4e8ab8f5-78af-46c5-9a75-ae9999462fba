# Technical Specification - Project Synapse

## Implementation Details & Component Specifications

### 1. Project Structure
```
project-synapse/
├── src/
│   ├── api/
│   │   ├── __init__.py
│   │   ├── main.py                 # FastAPI application
│   │   ├── auth.py                 # Authentication middleware
│   │   ├── routes/
│   │   │   ├── __init__.py
│   │   │   ├── documents.py        # Document upload/management
│   │   │   ├── query.py            # Query endpoints
│   │   │   └── graph.py            # Graph exploration endpoints
│   │   └── middleware/
│   │       ├── __init__.py
│   │       ├── cors.py
│   │       └── rate_limiting.py
│   ├── services/
│   │   ├── __init__.py
│   │   ├── preprocessing.py        # Text extraction & chunking
│   │   ├── knowledge_extraction.py # OpenIE service
│   │   ├── embedding_service.py    # Mock embedding service
│   │   ├── graph_service.py        # Neo4j operations
│   │   ├── metadata_service.py     # PostgreSQL operations
│   │   └── query_orchestrator.py   # GraphRAG pipeline
│   ├── models/
│   │   ├── __init__.py
│   │   ├── document.py             # Document data models
│   │   ├── graph.py                # Graph entity models
│   │   └── query.py                # Query request/response models
│   ├── workers/
│   │   ├── __init__.py
│   │   ├── celery_app.py           # Celery configuration
│   │   └── tasks.py                # Background tasks
│   ├── database/
│   │   ├── __init__.py
│   │   ├── neo4j_client.py         # Neo4j connection
│   │   ├── postgres_client.py      # PostgreSQL connection
│   │   └── redis_client.py         # Redis connection
│   ├── utils/
│   │   ├── __init__.py
│   │   ├── config.py               # Configuration management
│   │   ├── logging.py              # Logging setup
│   │   └── exceptions.py           # Custom exceptions
│   └── tests/
│       ├── __init__.py
│       ├── test_api/
│       ├── test_services/
│       └── test_workers/
├── docker/
│   ├── Dockerfile.api
│   ├── Dockerfile.worker
│   └── docker-compose.yml
├── config/
│   ├── development.env
│   ├── staging.env
│   └── production.env
├── requirements/
│   ├── base.txt
│   ├── development.txt
│   └── production.txt
├── scripts/
│   ├── setup_database.py
│   └── run_migrations.py
├── docs/
│   ├── api_documentation.md
│   └── deployment_guide.md
├── .env.example
├── .gitignore
├── README.md
└── pyproject.toml
```

### 2. Core Dependencies

**Base Requirements (requirements/base.txt)**
```txt
fastapi==0.104.1
uvicorn[standard]==0.24.0
celery==5.3.4
redis==5.0.1
neo4j==5.14.1
psycopg2-binary==2.9.9
sqlalchemy==2.0.23
alembic==1.12.1
pydantic==2.5.0
python-multipart==0.0.6
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
openai==1.3.7
unstructured[pdf]==0.11.6
langchain==0.0.350
sentence-transformers==2.2.2
numpy==1.24.3
pandas==2.1.4
python-dotenv==1.0.0
structlog==23.2.0
```

### 3. Configuration Management

**config/settings.py**
```python
from pydantic_settings import BaseSettings
from typing import Optional
import os

class Settings(BaseSettings):
    # API Configuration
    API_HOST: str = "0.0.0.0"
    API_PORT: int = 8000
    API_WORKERS: int = 4
    
    # Database URLs
    NEO4J_URI: str = "bolt://localhost:7687"
    NEO4J_USER: str = "neo4j"
    NEO4J_PASSWORD: str = "password"
    
    POSTGRES_URL: str = "postgresql://user:password@localhost:5432/synapse"
    REDIS_URL: str = "redis://localhost:6379/0"
    
    # OpenAI Configuration
    OPENAI_API_KEY: str
    OPENAI_MODEL: str = "gpt-4"
    OPENAI_MAX_TOKENS: int = 2000
    OPENAI_TEMPERATURE: float = 0.1
    
    # Processing Configuration
    CHUNK_SIZE: int = 1000
    CHUNK_OVERLAP: int = 150
    MAX_FILE_SIZE: int = 50 * 1024 * 1024  # 50MB
    SUPPORTED_FORMATS: list = ["pdf", "docx", "txt", "md", "html"]
    
    # Security
    SECRET_KEY: str
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    
    # Celery Configuration
    CELERY_BROKER_URL: str = "redis://localhost:6379/1"
    CELERY_RESULT_BACKEND: str = "redis://localhost:6379/2"
    
    # Mock Embedding Configuration
    EMBEDDING_DIMENSION: int = 768
    
    class Config:
        env_file = ".env"
        case_sensitive = True

settings = Settings()
```

### 4. Data Models

**models/document.py**
```python
from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any
from datetime import datetime
from enum import Enum
import uuid

class ProcessingStatus(str, Enum):
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"

class DocumentUpload(BaseModel):
    filename: str
    content_type: str
    size: int

class DocumentMetadata(BaseModel):
    id: uuid.UUID = Field(default_factory=uuid.uuid4)
    filename: str
    file_type: str
    upload_timestamp: datetime = Field(default_factory=datetime.utcnow)
    processing_status: ProcessingStatus = ProcessingStatus.PENDING
    metadata: Optional[Dict[str, Any]] = None
    error_message: Optional[str] = None

class TextChunk(BaseModel):
    id: uuid.UUID = Field(default_factory=uuid.uuid4)
    document_id: uuid.UUID
    chunk_text: str
    chunk_index: int
    token_count: int
    created_at: datetime = Field(default_factory=datetime.utcnow)

class DocumentResponse(BaseModel):
    id: uuid.UUID
    filename: str
    status: ProcessingStatus
    upload_timestamp: datetime
    chunk_count: Optional[int] = None
    entity_count: Optional[int] = None
```

**models/graph.py**
```python
from pydantic import BaseModel
from typing import List, Dict, Optional, Any
from enum import Enum

class EntityType(str, Enum):
    PERSON = "person"
    ORGANIZATION = "organization"
    LOCATION = "location"
    CONCEPT = "concept"
    EVENT = "event"
    OTHER = "other"

class Entity(BaseModel):
    name: str
    type: EntityType = EntityType.OTHER
    properties: Dict[str, Any] = {}
    confidence: float = 1.0

class Relationship(BaseModel):
    subject: str
    predicate: str
    object: str
    confidence: float = 1.0
    source_chunk_id: Optional[str] = None

class Triple(BaseModel):
    subject: Entity
    relationship: str
    object: Entity
    confidence: float = 1.0
    context: Optional[str] = None

class GraphNode(BaseModel):
    id: str
    name: str
    type: EntityType
    properties: Dict[str, Any] = {}
    relationships: List[Dict[str, Any]] = []

class GraphPath(BaseModel):
    nodes: List[GraphNode]
    relationships: List[Dict[str, Any]]
    path_length: int
    relevance_score: float
```

**models/query.py**
```python
from pydantic import BaseModel
from typing import List, Optional, Dict, Any
from datetime import datetime

class QueryRequest(BaseModel):
    query: str
    context_limit: int = 10
    max_graph_depth: int = 3
    include_sources: bool = True
    user_id: Optional[str] = None

class SourceCitation(BaseModel):
    document_id: str
    document_name: str
    chunk_id: str
    chunk_text: str
    relevance_score: float

class QueryResponse(BaseModel):
    answer: str
    confidence: float
    sources: List[SourceCitation]
    processing_time: float
    graph_nodes_explored: int
    timestamp: datetime = Field(default_factory=datetime.utcnow)

class GraphExplorationRequest(BaseModel):
    start_entities: List[str]
    max_depth: int = 2
    relationship_types: Optional[List[str]] = None
    limit: int = 50

class GraphExplorationResponse(BaseModel):
    subgraph: Dict[str, Any]
    node_count: int
    relationship_count: int
    exploration_depth: int
```

### 5. OpenIE Knowledge Extraction Service

**services/knowledge_extraction.py**
```python
import openai
from typing import List, Dict, Any
import json
import re
from models.graph import Triple, Entity, EntityType
from utils.config import settings

class KnowledgeExtractionService:
    def __init__(self):
        openai.api_key = settings.OPENAI_API_KEY
        self.model = settings.OPENAI_MODEL
        
    def extract_triples(self, text_chunk: str) -> List[Triple]:
        """Extract knowledge triples from text using OpenAI GPT-4"""
        
        prompt = self._build_extraction_prompt(text_chunk)
        
        try:
            response = openai.ChatCompletion.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": self._get_system_prompt()},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=settings.OPENAI_MAX_TOKENS,
                temperature=settings.OPENAI_TEMPERATURE
            )
            
            extracted_data = response.choices[0].message.content
            triples = self._parse_extraction_response(extracted_data)
            return triples
            
        except Exception as e:
            print(f"Error in knowledge extraction: {e}")
            return []
    
    def _get_system_prompt(self) -> str:
        return """You are an expert knowledge extraction system. Your task is to extract structured knowledge from text in the form of subject-predicate-object triples.

Guidelines:
1. Extract only factual, significant relationships
2. Use clear, normalized entity names
3. Use descriptive, standardized predicates
4. Assign confidence scores based on certainty
5. Classify entities by type when possible
6. Focus on core facts and meaningful connections

Return results as valid JSON array of objects with this structure:
{
  "subject": {"name": "entity_name", "type": "entity_type"},
  "predicate": "relationship_description",
  "object": {"name": "entity_name", "type": "entity_type"},
  "confidence": 0.95,
  "context": "relevant_text_snippet"
}

Entity types: person, organization, location, concept, event, other"""

    def _build_extraction_prompt(self, text: str) -> str:
        return f"""Extract all significant knowledge triples from the following text:

TEXT:
{text}

Return a JSON array of triples following the specified format. Focus on extracting:
- Key entities and their relationships
- Important facts and connections
- Hierarchical relationships
- Temporal relationships
- Causal relationships

Ensure entity names are normalized and predicates are descriptive."""

    def _parse_extraction_response(self, response: str) -> List[Triple]:
        """Parse the OpenAI response into Triple objects"""
        try:
            # Clean the response to extract JSON
            json_match = re.search(r'\[.*\]', response, re.DOTALL)
            if not json_match:
                return []
            
            json_str = json_match.group(0)
            data = json.loads(json_str)
            
            triples = []
            for item in data:
                try:
                    subject = Entity(
                        name=item["subject"]["name"],
                        type=EntityType(item["subject"].get("type", "other"))
                    )
                    object_entity = Entity(
                        name=item["object"]["name"],
                        type=EntityType(item["object"].get("type", "other"))
                    )
                    
                    triple = Triple(
                        subject=subject,
                        relationship=item["predicate"],
                        object=object_entity,
                        confidence=item.get("confidence", 0.8),
                        context=item.get("context", "")
                    )
                    triples.append(triple)
                    
                except (KeyError, ValueError) as e:
                    print(f"Error parsing triple: {e}")
                    continue
            
            return triples
            
        except json.JSONDecodeError as e:
            print(f"Error parsing JSON response: {e}")
            return []
```

### 6. Mock Embedding Service

**services/embedding_service.py**
```python
import numpy as np
from typing import List, Dict, Any
import hashlib
from utils.config import settings

class MockEmbeddingService:
    """Mock embedding service that generates consistent dummy vectors"""
    
    def __init__(self):
        self.dimension = settings.EMBEDDING_DIMENSION
        self.cache = {}
    
    def generate_embedding(self, text: str) -> List[float]:
        """Generate a consistent mock embedding for text"""
        
        # Use text hash to generate consistent vectors
        text_hash = hashlib.md5(text.encode()).hexdigest()
        
        if text_hash in self.cache:
            return self.cache[text_hash]
        
        # Generate pseudo-random but consistent vector
        np.random.seed(int(text_hash[:8], 16))
        vector = np.random.normal(0, 1, self.dimension)
        
        # Normalize the vector
        vector = vector / np.linalg.norm(vector)
        
        # Cache the result
        embedding = vector.tolist()
        self.cache[text_hash] = embedding
        
        return embedding
    
    def batch_generate_embeddings(self, texts: List[str]) -> List[List[float]]:
        """Generate embeddings for multiple texts"""
        return [self.generate_embedding(text) for text in texts]
    
    def calculate_similarity(self, embedding1: List[float], embedding2: List[float]) -> float:
        """Calculate cosine similarity between two embeddings"""
        vec1 = np.array(embedding1)
        vec2 = np.array(embedding2)
        
        dot_product = np.dot(vec1, vec2)
        norm1 = np.linalg.norm(vec1)
        norm2 = np.linalg.norm(vec2)
        
        if norm1 == 0 or norm2 == 0:
            return 0.0
        
        return dot_product / (norm1 * norm2)
    
    def find_similar_chunks(self, query_embedding: List[float], 
                           chunk_embeddings: Dict[str, List[float]], 
                           top_k: int = 10) -> List[Dict[str, Any]]:
        """Find most similar chunks to query (mock implementation)"""
        
        similarities = []
        for chunk_id, chunk_embedding in chunk_embeddings.items():
            similarity = self.calculate_similarity(query_embedding, chunk_embedding)
            similarities.append({
                "chunk_id": chunk_id,
                "similarity": similarity
            })
        
        # Sort by similarity and return top_k
        similarities.sort(key=lambda x: x["similarity"], reverse=True)
        return similarities[:top_k]
```

### 7. Neo4j Graph Service

**services/graph_service.py**
```python
from neo4j import GraphDatabase
from typing import List, Dict, Any, Optional
from models.graph import Triple, Entity, GraphNode, GraphPath
from utils.config import settings
import uuid

class GraphService:
    def __init__(self):
        self.driver = GraphDatabase.driver(
            settings.NEO4J_URI,
            auth=(settings.NEO4J_USER, settings.NEO4J_PASSWORD)
        )
    
    def close(self):
        self.driver.close()
    
    def create_constraints(self):
        """Create necessary constraints and indexes"""
        with self.driver.session() as session:
            # Create constraints
            session.run("""
                CREATE CONSTRAINT entity_name IF NOT EXISTS 
                FOR (e:Entity) REQUIRE e.name IS UNIQUE
            """)
            
            session.run("""
                CREATE CONSTRAINT document_id IF NOT EXISTS 
                FOR (d:Document) REQUIRE d.id IS UNIQUE
            """)
            
            session.run("""
                CREATE CONSTRAINT chunk_id IF NOT EXISTS 
                FOR (c:Chunk) REQUIRE c.id IS UNIQUE
            """)
            
            # Create indexes for performance
            session.run("CREATE INDEX entity_type IF NOT EXISTS FOR (e:Entity) ON (e.type)")
            session.run("CREATE INDEX chunk_document IF NOT EXISTS FOR (c:Chunk) ON (c.document_id)")
    
    def store_triples(self, triples: List[Triple], chunk_id: str) -> bool:
        """Store extracted triples in the graph database"""
        
        with self.driver.session() as session:
            try:
                for triple in triples:
                    # Create or merge entities and relationships
                    query = """
                    MERGE (s:Entity {name: $subject_name})
                    SET s.type = $subject_type,
                        s.updated_at = datetime()
                    
                    MERGE (o:Entity {name: $object_name})
                    SET o.type = $object_type,
                        o.updated_at = datetime()
                    
                    MERGE (c:Chunk {id: $chunk_id})
                    
                    MERGE (s)-[r:RELATES_TO {
                        type: $relationship,
                        confidence: $confidence,
                        chunk_id: $chunk_id
                    }]->(o)
                    
                    MERGE (c)-[:MENTIONS]->(s)
                    MERGE (c)-[:MENTIONS]->(o)
                    """
                    
                    session.run(query, {
                        "subject_name": triple.subject.name,
                        "subject_type": triple.subject.type.value,
                        "object_name": triple.object.name,
                        "object_type": triple.object.type.value,
                        "relationship": triple.relationship,
                        "confidence": triple.confidence,
                        "chunk_id": chunk_id
                    })
                
                return True
                
            except Exception as e:
                print(f"Error storing triples: {e}")
                return False
    
    def find_entities_by_names(self, entity_names: List[str]) -> List[GraphNode]:
        """Find entities by their names"""
        
        with self.driver.session() as session:
            query = """
            MATCH (e:Entity)
            WHERE e.name IN $entity_names
            OPTIONAL MATCH (e)-[r]->(related:Entity)
            RETURN e, collect({
                type: type(r),
                target: related.name,
                confidence: r.confidence
            }) as relationships
            """
            
            result = session.run(query, {"entity_names": entity_names})
            
            nodes = []
            for record in result:
                entity = record["e"]
                relationships = record["relationships"]
                
                node = GraphNode(
                    id=entity["name"],
                    name=entity["name"],
                    type=entity.get("type", "other"),
                    properties=dict(entity),
                    relationships=relationships
                )
                nodes.append(node)
            
            return nodes
    
    def explore_subgraph(self, start_entities: List[str], 
                        max_depth: int = 2, 
                        relationship_types: Optional[List[str]] = None) -> Dict[str, Any]:
        """Explore subgraph starting from given entities"""
        
        with self.driver.session() as session:
            # Build relationship filter
            rel_filter = ""
            if relationship_types:
                rel_filter = f"AND type(r) IN {relationship_types}"
            
            query = f"""
            MATCH path = (start:Entity)-[r*1..{max_depth}]-(end:Entity)
            WHERE start.name IN $start_entities {rel_filter}
            WITH path, relationships(path) as rels, nodes(path) as nodes
            RETURN 
                [n IN nodes | {{
                    name: n.name, 
                    type: n.type,
                    properties: properties(n)
                }}] as path_nodes,
                [r IN rels | {{
                    type: type(r),
                    confidence: r.confidence,
                    source: startNode(r).name,
                    target: endNode(r).name
                }}] as path_relationships,
                length(path) as path_length
            ORDER BY path_length
            LIMIT 100
            """
            
            result = session.run(query, {"start_entities": start_entities})
            
            # Process results into subgraph structure
            nodes = {}
            relationships = []
            
            for record in result:
                path_nodes = record["path_nodes"]
                path_relationships = record["path_relationships"]
                
                # Add nodes
                for node in path_nodes:
                    nodes[node["name"]] = node
                
                # Add relationships
                relationships.extend(path_relationships)
            
            return {
                "nodes": list(nodes.values()),
                "relationships": relationships,
                "node_count": len(nodes),
                "relationship_count": len(relationships)
            }
    
    def get_entity_context_chunks(self, entity_names: List[str]) -> List[str]:
        """Get all chunk IDs that mention the given entities"""
        
        with self.driver.session() as session:
            query = """
            MATCH (e:Entity)-[:MENTIONED_IN]-(c:Chunk)
            WHERE e.name IN $entity_names
            RETURN DISTINCT c.id as chunk_id
            """
            
            result = session.run(query, {"entity_names": entity_names})
            return [record["chunk_id"] for record in result]
```

This technical specification provides detailed implementation guidance for each component of the system. The architecture focuses on:

1. **Modular Design**: Clear separation of concerns with dedicated services
2. **OpenAI Integration**: Proper GPT-4 integration for knowledge extraction and answer synthesis
3. **Mock Embedding Service**: Placeholder that maintains consistent behavior for testing
4. **Neo4j Focus**: Comprehensive graph storage and retrieval operations
5. **Scalable Architecture**: Async processing with Celery and proper database design
6. **Type Safety**: Comprehensive Pydantic models for data validation

The next step would be to implement these components following this specification. Would you like me to proceed with the implementation phase, or would you like to review and modify any aspects of this architecture?