# API Configuration
API_HOST=0.0.0.0
API_PORT=8000
API_WORKERS=4

# Database Configuration
NEO4J_URI=bolt://localhost:7687
NEO4J_USER=neo4j
NEO4J_PASSWORD=your_secure_password_here

POSTGRES_URL=postgresql://synapse_user:your_postgres_password@localhost:5432/synapse
POSTGRES_USER=synapse_user
POSTGRES_PASSWORD=your_postgres_password
POSTGRES_DB=synapse

REDIS_URL=redis://localhost:6379/0

# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_MODEL=gpt-4
OPENAI_MAX_TOKENS=2000
OPENAI_TEMPERATURE=0.1

# Processing Configuration
CHUNK_SIZE=1000
CHUNK_OVERLAP=150
MAX_FILE_SIZE=52428800
SUPPORTED_FORMATS=["pdf","docx","txt","md","html"]

# Security
SECRET_KEY=your_very_secure_secret_key_here_minimum_32_characters
ACCESS_TOKEN_EXPIRE_MINUTES=30

# Celery Configuration
CELERY_BROKER_URL=redis://localhost:6379/1
CELERY_RESULT_BACKEND=redis://localhost:6379/2

# Mock Embedding Configuration
EMBEDDING_DIMENSION=768

# Environment
ENVIRONMENT=development
DEBUG=true