# Project Synapse - Dynamic Knowledge Graph & GraphRAG System Architecture

## Overview
This document outlines the architecture for Project Synapse, a dynamic knowledge management platform that can ingest any document type and build a flexible, schema-less knowledge graph using OpenIE, with sophisticated GraphRAG capabilities for contextual query answering.

## Technology Stack
- **Backend Framework**: Python with FastAPI
- **Task Queue**: Celery with Redis broker
- **Graph Database**: Neo4j
- **Metadata Database**: PostgreSQL
- **Vector Storage**: Mock embedding service (placeholder)
- **LLM Service**: OpenAI GPT-4 API
- **Containerization**: Docker & Docker Compose
- **Authentication**: JWT tokens

## System Architecture

### High-Level Architecture

```mermaid
graph TB
    subgraph "Client Layer"
        UI[Web Interface]
        API_CLIENT[API Clients]
    end
    
    subgraph "API Gateway"
        GATEWAY[FastAPI Gateway]
        AUTH[Authentication Service]
    end
    
    subgraph "Ingestion Pipeline (Async)"
        UPLOAD[Document Upload API]
        QUEUE[Redis Queue]
        PREPROCESS[Preprocessing Service]
        EXTRACT[Knowledge Extraction Service]
        EMBED[Mock Embedding Service]
    end
    
    subgraph "Storage Layer"
        NEO4J[(Neo4j Graph DB)]
        POSTGRES[(PostgreSQL)]
        REDIS[(Redis Cache)]
    end
    
    subgraph "Retrieval Pipeline (Sync)"
        QUERY_API[Query API]
        ORCHESTRATOR[Query Orchestrator]
        GRAPH_SEARCH[Graph Explorer]
        SYNTHESIS[Answer Synthesis]
    end
    
    subgraph "External Services"
        OPENAI[OpenAI GPT-4 API]
    end
    
    UI --> GATEWAY
    API_CLIENT --> GATEWAY
    GATEWAY --> AUTH
    GATEWAY --> UPLOAD
    GATEWAY --> QUERY_API
    
    UPLOAD --> QUEUE
    QUEUE --> PREPROCESS
    PREPROCESS --> EXTRACT
    EXTRACT --> EMBED
    EMBED --> NEO4J
    EMBED --> POSTGRES
    
    QUERY_API --> ORCHESTRATOR
    ORCHESTRATOR --> GRAPH_SEARCH
    GRAPH_SEARCH --> NEO4J
    ORCHESTRATOR --> SYNTHESIS
    SYNTHESIS --> OPENAI
    
    EXTRACT --> OPENAI
    PREPROCESS --> REDIS
    ORCHESTRATOR --> POSTGRES
```

### Component Architecture

#### 1. Ingestion Pipeline Components

**Document Upload Service**
- FastAPI endpoint for secure document uploads
- Supports PDF, DOCX, TXT, MD, HTML formats
- File validation and virus scanning
- Queues documents for asynchronous processing

**Preprocessing Service**
- Celery worker for text extraction using `unstructured.io`
- Text cleaning and normalization
- Semantic chunking with RecursiveCharacterTextSplitter
- Chunk size: 1000 characters, overlap: 150 characters

**Knowledge Extraction Service**
- OpenIE using OpenAI GPT-4 with specialized prompts
- Extracts (subject, predicate, object) triples
- Entity normalization and deduplication
- Relationship type standardization

**Mock Embedding Service**
- Placeholder service returning dummy vectors
- Maintains consistent vector dimensions (768-dim)
- Simulates semantic similarity calculations
- Ready for future integration with real embedding models

#### 2. Storage Layer

**Neo4j Graph Database Schema**
```cypher
// Node types
CREATE CONSTRAINT entity_name IF NOT EXISTS FOR (e:Entity) REQUIRE e.name IS UNIQUE;
CREATE CONSTRAINT document_id IF NOT EXISTS FOR (d:Document) REQUIRE d.id IS UNIQUE;
CREATE CONSTRAINT chunk_id IF NOT EXISTS FOR (c:Chunk) REQUIRE c.id IS UNIQUE;

// Relationship types
(:Entity)-[:RELATES_TO {type: string, confidence: float}]->(:Entity)
(:Document)-[:CONTAINS]->(:Chunk)
(:Chunk)-[:MENTIONS]->(:Entity)
```

**PostgreSQL Metadata Schema**
```sql
-- Documents table
CREATE TABLE documents (
    id UUID PRIMARY KEY,
    filename VARCHAR(255) NOT NULL,
    file_type VARCHAR(50) NOT NULL,
    upload_timestamp TIMESTAMP DEFAULT NOW(),
    processing_status VARCHAR(50) DEFAULT 'pending',
    metadata JSONB
);

-- Chunks table
CREATE TABLE chunks (
    id UUID PRIMARY KEY,
    document_id UUID REFERENCES documents(id),
    chunk_text TEXT NOT NULL,
    chunk_index INTEGER NOT NULL,
    token_count INTEGER,
    created_at TIMESTAMP DEFAULT NOW()
);

-- Graph-chunk linking table
CREATE TABLE graph_chunk_links (
    id UUID PRIMARY KEY,
    node_id VARCHAR(255) NOT NULL,
    chunk_id UUID REFERENCES chunks(id),
    relationship_type VARCHAR(100),
    confidence_score FLOAT
);
```

#### 3. Retrieval Pipeline Components

**Query Orchestrator**
- Central brain of the retrieval system
- Coordinates multi-step retrieval process
- Implements GraphRAG algorithm
- Manages context augmentation

**Graph Explorer**
- Implements graph traversal algorithms
- Personalized PageRank for entity ranking
- Multi-hop relationship discovery
- Subgraph extraction for context

**Answer Synthesis Service**
- Formats augmented context for LLM
- Generates final answers using OpenAI GPT-4
- Provides source citations and confidence scores
- Handles follow-up question generation

### Data Flow

#### Ingestion Flow
1. **Document Upload** → Validate → Store metadata → Queue for processing
2. **Preprocessing** → Extract text → Clean → Chunk → Store chunks
3. **Knowledge Extraction** → LLM analysis → Extract triples → Normalize entities
4. **Graph Storage** → Create/merge nodes → Create relationships → Link to chunks
5. **Embedding Generation** → Mock vectors → Store associations

#### Query Flow
1. **Query Reception** → Validate → Parse intent
2. **Initial Retrieval** → Mock semantic search → Get relevant chunks
3. **Entity Identification** → Extract entities from chunks → Map to graph nodes
4. **Graph Exploration** → Traverse relationships → Build context subgraph
5. **Context Augmentation** → Gather related chunks → Rank by relevance
6. **Answer Generation** → Format context → LLM synthesis → Return with citations

## API Design

### Ingestion Endpoints
```python
POST /api/v1/documents/upload
- Multipart file upload
- Returns: document_id, processing_status

GET /api/v1/documents/{document_id}/status
- Returns: processing_status, progress, errors

GET /api/v1/documents
- Returns: paginated list of documents
```

### Query Endpoints
```python
POST /api/v1/query
- Body: {"query": "user question", "context_limit": 10}
- Returns: {"answer": "...", "sources": [...], "confidence": 0.85}

GET /api/v1/graph/entities/{entity_name}
- Returns: entity details and relationships

GET /api/v1/graph/explore
- Query params: start_entities, max_depth, relationship_types
- Returns: subgraph data
```

## Scalability Considerations

### Horizontal Scaling
- **API Layer**: Multiple FastAPI instances behind load balancer
- **Worker Layer**: Auto-scaling Celery workers based on queue length
- **Database Layer**: Neo4j clustering, PostgreSQL read replicas

### Performance Optimizations
- **Caching**: Redis for frequently accessed graph patterns
- **Indexing**: Neo4j indexes on entity names and relationship types
- **Batch Processing**: Bulk operations for large document sets
- **Connection Pooling**: Database connection management

### Resource Management
- **Memory**: Chunked processing for large documents
- **CPU**: Parallel processing for independent tasks
- **I/O**: Asynchronous operations throughout the pipeline

## Security Architecture

### Authentication & Authorization
- JWT-based authentication
- Role-based access control (Admin, Power User, End User)
- API key management for external integrations

### Data Security
- Encryption at rest (database level)
- Encryption in transit (TLS/SSL)
- Input validation and sanitization
- Rate limiting and DDoS protection

## Monitoring & Observability

### Metrics
- Document processing throughput
- Query response times
- Graph growth metrics
- API endpoint performance

### Logging
- Structured logging with correlation IDs
- Error tracking and alerting
- Audit trails for data access

### Health Checks
- Service health endpoints
- Database connectivity checks
- External service availability

## Deployment Architecture

### Docker Composition
```yaml
services:
  api-gateway:
    build: ./api-gateway
    ports: ["8000:8000"]
  
  celery-worker:
    build: ./workers
    depends_on: [redis, postgres, neo4j]
  
  neo4j:
    image: neo4j:5.0
    environment:
      NEO4J_AUTH: neo4j/password
  
  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: synapse
  
  redis:
    image: redis:7-alpine
```

### Environment Configuration
- Development: Docker Compose
- Staging: Kubernetes cluster
- Production: Managed cloud services (AWS/GCP/Azure)

## Future Enhancements

### Phase 2 Features
- Real-time graph updates
- Advanced entity disambiguation
- Graph visualization interface
- User feedback integration

### Scalability Improvements
- Distributed graph processing
- Vector database integration
- Advanced caching strategies
- Multi-tenant architecture

## Success Metrics

### Technical Metrics
- **Ingestion Rate**: Documents processed per hour
- **Query Latency**: P95 response time < 15 seconds
- **Graph Density**: Relationships per entity
- **System Uptime**: 99.9% availability

### Business Metrics
- **Answer Relevance**: Human evaluation score > 4.0/5.0
- **User Adoption**: Active users and query volume
- **Knowledge Coverage**: Entities extracted per document
- **Query Success Rate**: Percentage of satisfactory answers

This architecture provides a robust, scalable foundation for the Dynamic Knowledge Graph & GraphRAG System while maintaining flexibility for future enhancements and optimizations.