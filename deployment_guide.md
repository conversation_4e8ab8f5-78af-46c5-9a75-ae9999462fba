# Deployment Guide - Project Synapse

## Overview
This guide provides comprehensive deployment instructions for the Project Synapse Dynamic Knowledge Graph & GraphRAG System, including Docker containerization, environment setup, and production deployment considerations.

## Quick Start with Docker Compose

### 1. Environment Setup

**Create `.env` file:**
```bash
# API Configuration
API_HOST=0.0.0.0
API_PORT=8000
API_WORKERS=4

# Database Configuration
NEO4J_URI=bolt://neo4j:7687
NEO4J_USER=neo4j
NEO4J_PASSWORD=your_secure_password_here

POSTGRES_URL=**************************************************************/synapse
POSTGRES_USER=synapse_user
POSTGRES_PASSWORD=your_postgres_password
POSTGRES_DB=synapse

REDIS_URL=redis://redis:6379/0

# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_MODEL=gpt-4
OPENAI_MAX_TOKENS=2000
OPENAI_TEMPERATURE=0.1

# Processing Configuration
CHUNK_SIZE=1000
CHUNK_OVERLAP=150
MAX_FILE_SIZE=52428800
SUPPORTED_FORMATS=["pdf","docx","txt","md","html"]

# Security
SECRET_KEY=your_very_secure_secret_key_here_minimum_32_characters
ACCESS_TOKEN_EXPIRE_MINUTES=30

# Celery Configuration
CELERY_BROKER_URL=redis://redis:6379/1
CELERY_RESULT_BACKEND=redis://redis:6379/2

# Mock Embedding Configuration
EMBEDDING_DIMENSION=768
```

### 2. Docker Compose Configuration

**docker-compose.yml:**
```yaml
version: '3.8'

services:
  # API Gateway Service
  api:
    build:
      context: .
      dockerfile: docker/Dockerfile.api
    ports:
      - "8000:8000"
    environment:
      - API_HOST=0.0.0.0
      - API_PORT=8000
    env_file:
      - .env
    depends_on:
      - postgres
      - neo4j
      - redis
    volumes:
      - ./uploads:/app/uploads
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Celery Worker Service
  worker:
    build:
      context: .
      dockerfile: docker/Dockerfile.worker
    env_file:
      - .env
    depends_on:
      - postgres
      - neo4j
      - redis
    volumes:
      - ./uploads:/app/uploads
    restart: unless-stopped
    deploy:
      replicas: 2
    healthcheck:
      test: ["CMD", "celery", "-A", "src.workers.celery_app", "inspect", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Celery Beat Scheduler (for periodic tasks)
  scheduler:
    build:
      context: .
      dockerfile: docker/Dockerfile.worker
    command: celery -A src.workers.celery_app beat --loglevel=info
    env_file:
      - .env
    depends_on:
      - redis
    restart: unless-stopped

  # Neo4j Graph Database
  neo4j:
    image: neo4j:5.15-community
    ports:
      - "7474:7474"  # HTTP
      - "7687:7687"  # Bolt
    environment:
      - NEO4J_AUTH=neo4j/${NEO4J_PASSWORD}
      - NEO4J_PLUGINS=["apoc"]
      - NEO4J_dbms_security_procedures_unrestricted=apoc.*
      - NEO4J_dbms_memory_heap_initial__size=512m
      - NEO4J_dbms_memory_heap_max__size=2G
      - NEO4J_dbms_memory_pagecache_size=1G
    volumes:
      - neo4j_data:/data
      - neo4j_logs:/logs
      - neo4j_import:/var/lib/neo4j/import
      - neo4j_plugins:/plugins
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "cypher-shell", "-u", "neo4j", "-p", "${NEO4J_PASSWORD}", "RETURN 1"]
      interval: 30s
      timeout: 10s
      retries: 5

  # PostgreSQL Metadata Database
  postgres:
    image: postgres:15-alpine
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - POSTGRES_DB=${POSTGRES_DB}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init_postgres.sql:/docker-entrypoint-initdb.d/init.sql
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER} -d ${POSTGRES_DB}"]
      interval: 30s
      timeout: 10s
      retries: 5

  # Redis Cache and Message Broker
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes --maxmemory 512mb --maxmemory-policy allkeys-lru
    volumes:
      - redis_data:/data
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx Reverse Proxy (Production)
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
    depends_on:
      - api
    restart: unless-stopped
    profiles:
      - production

volumes:
  neo4j_data:
  neo4j_logs:
  neo4j_import:
  neo4j_plugins:
  postgres_data:
  redis_data:

networks:
  default:
    driver: bridge
```

### 3. Dockerfile Configurations

**docker/Dockerfile.api:**
```dockerfile
FROM python:3.11-slim

# Set working directory
WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    curl \
    gcc \
    g++ \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements
COPY requirements/ requirements/
RUN pip install --no-cache-dir -r requirements/production.txt

# Copy application code
COPY src/ src/
COPY scripts/ scripts/

# Create uploads directory
RUN mkdir -p uploads

# Set Python path
ENV PYTHONPATH=/app

# Expose port
EXPOSE 8000

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# Run application
CMD ["uvicorn", "src.api.main:app", "--host", "0.0.0.0", "--port", "8000", "--workers", "4"]
```

**docker/Dockerfile.worker:**
```dockerfile
FROM python:3.11-slim

# Set working directory
WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    libpq-dev \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements
COPY requirements/ requirements/
RUN pip install --no-cache-dir -r requirements/production.txt

# Copy application code
COPY src/ src/
COPY scripts/ scripts/

# Create uploads directory
RUN mkdir -p uploads

# Set Python path
ENV PYTHONPATH=/app

# Default command (can be overridden)
CMD ["celery", "-A", "src.workers.celery_app", "worker", "--loglevel=info", "--concurrency=2"]
```

### 4. Database Initialization Scripts

**scripts/init_postgres.sql:**
```sql
-- Create database schema for Project Synapse

-- Documents table
CREATE TABLE IF NOT EXISTS documents (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    filename VARCHAR(255) NOT NULL,
    file_type VARCHAR(50) NOT NULL,
    file_size BIGINT NOT NULL,
    upload_timestamp TIMESTAMP DEFAULT NOW(),
    processing_status VARCHAR(50) DEFAULT 'pending',
    metadata JSONB DEFAULT '{}',
    error_message TEXT,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Chunks table
CREATE TABLE IF NOT EXISTS chunks (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    document_id UUID REFERENCES documents(id) ON DELETE CASCADE,
    chunk_text TEXT NOT NULL,
    chunk_index INTEGER NOT NULL,
    token_count INTEGER DEFAULT 0,
    embedding_vector FLOAT8[] DEFAULT NULL,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP DEFAULT NOW()
);

-- Graph-chunk linking table
CREATE TABLE IF NOT EXISTS graph_chunk_links (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    node_id VARCHAR(255) NOT NULL,
    chunk_id UUID REFERENCES chunks(id) ON DELETE CASCADE,
    relationship_type VARCHAR(100),
    confidence_score FLOAT DEFAULT 1.0,
    created_at TIMESTAMP DEFAULT NOW()
);

-- Users table (for authentication)
CREATE TABLE IF NOT EXISTS users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    username VARCHAR(100) UNIQUE NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    hashed_password VARCHAR(255) NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    is_admin BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Query logs table (for analytics)
CREATE TABLE IF NOT EXISTS query_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id),
    query_text TEXT NOT NULL,
    response_text TEXT,
    processing_time FLOAT,
    confidence_score FLOAT,
    sources_count INTEGER DEFAULT 0,
    graph_nodes_explored INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_documents_status ON documents(processing_status);
CREATE INDEX IF NOT EXISTS idx_documents_created ON documents(created_at);
CREATE INDEX IF NOT EXISTS idx_chunks_document ON chunks(document_id);
CREATE INDEX IF NOT EXISTS idx_chunks_created ON chunks(created_at);
CREATE INDEX IF NOT EXISTS idx_graph_links_node ON graph_chunk_links(node_id);
CREATE INDEX IF NOT EXISTS idx_graph_links_chunk ON graph_chunk_links(chunk_id);
CREATE INDEX IF NOT EXISTS idx_users_username ON users(username);
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_query_logs_user ON query_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_query_logs_created ON query_logs(created_at);

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at
CREATE TRIGGER update_documents_updated_at BEFORE UPDATE ON documents
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
```

**scripts/init_neo4j.py:**
```python
#!/usr/bin/env python3
"""
Neo4j initialization script for Project Synapse
"""

from neo4j import GraphDatabase
import os
from dotenv import load_dotenv

load_dotenv()

def initialize_neo4j():
    """Initialize Neo4j database with constraints and indexes"""
    
    uri = os.getenv("NEO4J_URI", "bolt://localhost:7687")
    user = os.getenv("NEO4J_USER", "neo4j")
    password = os.getenv("NEO4J_PASSWORD", "password")
    
    driver = GraphDatabase.driver(uri, auth=(user, password))
    
    with driver.session() as session:
        # Create constraints
        constraints = [
            "CREATE CONSTRAINT entity_name IF NOT EXISTS FOR (e:Entity) REQUIRE e.name IS UNIQUE",
            "CREATE CONSTRAINT document_id IF NOT EXISTS FOR (d:Document) REQUIRE d.id IS UNIQUE",
            "CREATE CONSTRAINT chunk_id IF NOT EXISTS FOR (c:Chunk) REQUIRE c.id IS UNIQUE",
            "CREATE CONSTRAINT user_id IF NOT EXISTS FOR (u:User) REQUIRE u.id IS UNIQUE"
        ]
        
        for constraint in constraints:
            try:
                session.run(constraint)
                print(f"✓ Created constraint: {constraint}")
            except Exception as e:
                print(f"⚠ Constraint may already exist: {e}")
        
        # Create indexes
        indexes = [
            "CREATE INDEX entity_type IF NOT EXISTS FOR (e:Entity) ON (e.type)",
            "CREATE INDEX entity_created IF NOT EXISTS FOR (e:Entity) ON (e.created_at)",
            "CREATE INDEX chunk_document IF NOT EXISTS FOR (c:Chunk) ON (c.document_id)",
            "CREATE INDEX document_status IF NOT EXISTS FOR (d:Document) ON (d.status)",
            "CREATE INDEX relationship_type IF NOT EXISTS FOR ()-[r:RELATES_TO]-() ON (r.type)",
            "CREATE INDEX relationship_confidence IF NOT EXISTS FOR ()-[r:RELATES_TO]-() ON (r.confidence)"
        ]
        
        for index in indexes:
            try:
                session.run(index)
                print(f"✓ Created index: {index}")
            except Exception as e:
                print(f"⚠ Index may already exist: {e}")
        
        # Create sample data structure (optional)
        sample_queries = [
            """
            MERGE (sys:System {name: 'Project Synapse', version: '1.0'})
            SET sys.initialized_at = datetime()
            """,
            """
            MERGE (root:Entity {name: 'ROOT', type: 'system'})
            SET root.description = 'Root node for system entities'
            """
        ]
        
        for query in sample_queries:
            try:
                session.run(query)
                print("✓ Created sample data structure")
            except Exception as e:
                print(f"⚠ Error creating sample data: {e}")
    
    driver.close()
    print("✅ Neo4j initialization completed")

if __name__ == "__main__":
    initialize_neo4j()
```

### 5. Nginx Configuration (Production)

**nginx/nginx.conf:**
```nginx
events {
    worker_connections 1024;
}

http {
    upstream api_backend {
        server api:8000;
    }
    
    # Rate limiting
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    
    server {
        listen 80;
        server_name your-domain.com;
        
        # Redirect HTTP to HTTPS
        return 301 https://$server_name$request_uri;
    }
    
    server {
        listen 443 ssl http2;
        server_name your-domain.com;
        
        # SSL Configuration
        ssl_certificate /etc/nginx/ssl/cert.pem;
        ssl_certificate_key /etc/nginx/ssl/key.pem;
        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
        
        # Security headers
        add_header X-Frame-Options DENY;
        add_header X-Content-Type-Options nosniff;
        add_header X-XSS-Protection "1; mode=block";
        add_header Strict-Transport-Security "max-age=31536000; includeSubDomains";
        
        # API routes
        location /api/ {
            limit_req zone=api burst=20 nodelay;
            
            proxy_pass http://api_backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # Timeouts
            proxy_connect_timeout 60s;
            proxy_send_timeout 60s;
            proxy_read_timeout 60s;
        }
        
        # File upload size
        client_max_body_size 50M;
        
        # Health check
        location /health {
            proxy_pass http://api_backend/health;
        }
        
        # Static files (if any)
        location /static/ {
            alias /app/static/;
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }
}
```

## Deployment Commands

### Development Deployment
```bash
# Clone repository
git clone <repository-url>
cd project-synapse

# Copy environment template
cp .env.example .env
# Edit .env with your configuration

# Build and start services
docker-compose up --build -d

# Initialize databases
docker-compose exec api python scripts/init_neo4j.py

# Check service health
docker-compose ps
docker-compose logs api
```

### Production Deployment
```bash
# Use production profile
docker-compose --profile production up -d

# Or use separate production compose file
docker-compose -f docker-compose.prod.yml up -d

# Monitor services
docker-compose logs -f --tail=100

# Scale workers if needed
docker-compose up -d --scale worker=4
```

## Monitoring and Maintenance

### Health Checks
```bash
# Check API health
curl http://localhost:8000/health

# Check database connections
docker-compose exec api python -c "
from src.database.neo4j_client import Neo4jClient
from src.database.postgres_client import PostgresClient
print('Neo4j:', Neo4jClient().test_connection())
print('PostgreSQL:', PostgresClient().test_connection())
"
```

### Log Management
```bash
# View logs
docker-compose logs -f api
docker-compose logs -f worker
docker-compose logs -f neo4j

# Log rotation (add to crontab)
docker system prune -f
```

### Backup Procedures
```bash
# PostgreSQL backup
docker-compose exec postgres pg_dump -U synapse_user synapse > backup_$(date +%Y%m%d).sql

# Neo4j backup
docker-compose exec neo4j neo4j-admin dump --database=neo4j --to=/backups/neo4j_$(date +%Y%m%d).dump
```

## Performance Tuning

### Database Optimization
- **Neo4j**: Adjust heap size based on available memory
- **PostgreSQL**: Configure shared_buffers, work_mem
- **Redis**: Set appropriate maxmemory and eviction policy

### Application Scaling
- Increase Celery worker replicas for heavy processing
- Use horizontal pod autoscaling in Kubernetes
- Implement connection pooling for databases

### Monitoring Metrics
- API response times
- Queue lengths and processing times
- Database query performance
- Memory and CPU usage
- Error rates and types

This deployment guide provides a complete production-ready setup for Project Synapse with proper security, monitoring, and scalability considerations.